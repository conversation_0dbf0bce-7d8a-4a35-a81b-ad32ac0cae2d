<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="confirm-content">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <el-icon :size="24" :color="iconColor">
            <component :is="iconComponent" />
          </el-icon>
        </div>
        <div class="flex-1">
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ title }}</h3>
          <p class="text-gray-600">{{ content }}</p>
          <div v-if="details" class="mt-3 p-3 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-700">{{ details }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button 
          :type="confirmType" 
          :loading="loading"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Warning, 
  QuestionFilled, 
  InfoFilled, 
  CircleCheckFilled,
  CircleCloseFilled 
} from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  title?: string
  content: string
  details?: string
  type?: 'warning' | 'info' | 'success' | 'error' | 'question'
  confirmText?: string
  cancelText?: string
  confirmType?: 'primary' | 'success' | 'warning' | 'danger'
  width?: string
  loading?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  type: 'warning',
  confirmText: '确定',
  cancelText: '取消',
  confirmType: 'primary',
  width: '400px',
  loading: false
})

const emit = defineEmits<Emits>()

// 图标组件
const iconComponent = computed(() => {
  switch (props.type) {
    case 'warning':
      return Warning
    case 'info':
      return InfoFilled
    case 'success':
      return CircleCheckFilled
    case 'error':
      return CircleCloseFilled
    case 'question':
    default:
      return QuestionFilled
  }
})

// 图标颜色
const iconColor = computed(() => {
  switch (props.type) {
    case 'warning':
      return '#E6A23C'
    case 'info':
      return '#409EFF'
    case 'success':
      return '#67C23A'
    case 'error':
      return '#F56C6C'
    case 'question':
    default:
      return '#909399'
  }
})

// 处理确认
function handleConfirm() {
  emit('confirm')
}

// 处理取消
function handleCancel() {
  emit('cancel')
  emit('update:visible', false)
}

// 处理关闭
function handleClose() {
  if (!props.loading) {
    handleCancel()
  }
}
</script>

<style scoped>
.confirm-content {
  padding: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
