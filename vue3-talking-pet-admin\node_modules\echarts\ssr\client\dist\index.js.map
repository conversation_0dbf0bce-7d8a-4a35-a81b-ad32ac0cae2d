{"version": 3, "file": "index.js", "sources": ["../lib/index.js"], "sourcesContent": ["\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport function hydrate(dom, options) {\n  var svgRoot = dom.querySelector('svg');\n  if (!svgRoot) {\n    console.error('No SVG element found in the DOM.');\n    return;\n  }\n  function getIndex(child, attr) {\n    var index = child.getAttribute(attr);\n    if (index) {\n      return parseInt(index, 10);\n    } else {\n      return undefined;\n    }\n  }\n  var listeners = options.on || {};\n  var _loop_1 = function (rawEvtName) {\n    if (!listeners.hasOwnProperty(rawEvtName)) {\n      return \"continue\";\n    }\n    var eventName = rawEvtName;\n    var listener = listeners[eventName];\n    if (!isFunction(listener)) {\n      return \"continue\";\n    }\n    svgRoot.addEventListener(eventName, function (event) {\n      var targetEl = event.target;\n      if (!targetEl || !isFunction(targetEl.getAttribute)) {\n        return;\n      }\n      var type = targetEl.getAttribute('ecmeta_ssr_type');\n      var silent = targetEl.getAttribute('ecmeta_silent') === 'true';\n      if (!type || silent) {\n        return;\n      }\n      listener({\n        type: eventName,\n        ssrType: type,\n        seriesIndex: getIndex(targetEl, 'ecmeta_series_index'),\n        dataIndex: getIndex(targetEl, 'ecmeta_data_index'),\n        event: event\n      });\n    });\n  };\n  for (var rawEvtName in listeners) {\n    _loop_1(rawEvtName);\n  }\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE;EACtC,EAAE,IAAI,OAAO,GAAG,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACzC,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;EACtD,IAAI,OAAO;EACX,GAAG;EACH,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;EACjC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;EACzC,IAAI,IAAI,KAAK,EAAE;EACf,MAAM,OAAO,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EACjC,KAAK,MAAM;EACX,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK;EACL,GAAG;EACH,EAAE,IAAI,SAAS,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;EACnC,EAAE,IAAI,OAAO,GAAG,UAAU,UAAU,EAAE;EACtC,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;EAC/C,MAAM,OAAO,UAAU,CAAC;EACxB,KAAK;EACL,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC;EAC/B,IAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;EACxC,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;EAC/B,MAAM,OAAO,UAAU,CAAC;EACxB,KAAK;EACL,IAAI,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAU,KAAK,EAAE;EACzD,MAAM,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;EAClC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;EAC3D,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;EAC1D,MAAM,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC;EACrE,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE;EAC3B,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,QAAQ,CAAC;EACf,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,OAAO,EAAE,IAAI;EACrB,QAAQ,WAAW,EAAE,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,CAAC;EAC9D,QAAQ,SAAS,EAAE,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EAC1D,QAAQ,KAAK,EAAE,KAAK;EACpB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;EACJ,EAAE,KAAK,IAAI,UAAU,IAAI,SAAS,EAAE;EACpC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;EACxB,GAAG;EACH,CAAC;EACD,SAAS,UAAU,CAAC,KAAK,EAAE;EAC3B,EAAE,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC;EACrC;;;;;;"}