<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">仪表盘</h1>
      <p class="text-gray-600 mt-1">欢迎回来，{{ authStore.userName }}！</p>
    </div>

    <!-- KPI 卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="(kpi, index) in kpiData"
        :key="index"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ kpi.title }}</p>
            <p class="text-2xl font-bold text-gray-900 mt-2">{{ kpi.value }}</p>
            <p class="text-sm text-gray-500 mt-1">
              <span :class="kpi.trend > 0 ? 'text-green-600' : 'text-red-600'">
                {{ kpi.trend > 0 ? '+' : '' }}{{ kpi.trend }}%
              </span>
              较昨日
            </p>
          </div>
          <div :class="kpi.iconBg" class="p-3 rounded-full">
            <el-icon :size="24" :color="kpi.iconColor">
              <component :is="kpi.icon" />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 销售趋势图 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">销售趋势</h3>
          <el-button size="small" @click="refreshSalesChart">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="h-80">
          <v-chart
            :option="salesChartOption"
            :loading="chartsLoading"
            class="w-full h-full"
          />
        </div>
      </div>

      <!-- 代理商业绩饼图 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">代理商业绩分布</h3>
          <el-button size="small" @click="refreshPerformanceChart">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="h-80">
          <v-chart
            :option="performanceChartOption"
            :loading="chartsLoading"
            class="w-full h-full"
          />
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <el-button
          v-if="authStore.isSuper"
          type="primary"
          :icon="Plus"
          @click="$router.push('/agents')"
        >
          添加代理商
        </el-button>
        
        <el-button
          v-if="authStore.isSuper"
          type="success"
          :icon="Tickets"
          @click="$router.push('/coupons/batches')"
        >
          创建优惠券批次
        </el-button>
        
        <el-button
          type="info"
          :icon="User"
          @click="$router.push('/users')"
        >
          查看用户
        </el-button>
        
        <el-button
          type="warning"
          :icon="Setting"
          @click="$router.push('/profile')"
        >
          个人设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { useAuthStore } from '@/store/auth'
import { getDashboardStats } from '@/api/admin'
import type { DashboardStats } from '@/types/admin'
import {
  User,
  TrendCharts,
  Money,
  UserFilled,
  Refresh,
  Plus,
  Tickets,
  Setting
} from '@element-plus/icons-vue'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const chartsLoading = ref(false)
const dashboardData = ref<DashboardStats | null>(null)

// KPI 数据
const kpiData = computed(() => {
  if (!dashboardData.value) {
    return [
      {
        title: '总用户数',
        value: '0',
        trend: 0,
        icon: User,
        iconColor: '#409EFF',
        iconBg: 'bg-blue-100'
      },
      {
        title: '总代理商数',
        value: '0',
        trend: 0,
        icon: UserFilled,
        iconColor: '#67C23A',
        iconBg: 'bg-green-100'
      },
      {
        title: '总销售额',
        value: '¥0',
        trend: 0,
        icon: Money,
        iconColor: '#E6A23C',
        iconBg: 'bg-yellow-100'
      },
      {
        title: '今日新增用户',
        value: '0',
        trend: 0,
        icon: TrendCharts,
        iconColor: '#F56C6C',
        iconBg: 'bg-red-100'
      }
    ]
  }

  const data = dashboardData.value
  return [
    {
      title: '总用户数',
      value: data.total_users.toLocaleString(),
      trend: 12.5,
      icon: User,
      iconColor: '#409EFF',
      iconBg: 'bg-blue-100'
    },
    {
      title: '总代理商数',
      value: data.total_agents.toLocaleString(),
      trend: 8.2,
      icon: UserFilled,
      iconColor: '#67C23A',
      iconBg: 'bg-green-100'
    },
    {
      title: '总销售额',
      value: `¥${data.total_sales.toLocaleString()}`,
      trend: 15.3,
      icon: Money,
      iconColor: '#E6A23C',
      iconBg: 'bg-yellow-100'
    },
    {
      title: '今日新增用户',
      value: data.today_new_users.toLocaleString(),
      trend: -2.1,
      icon: TrendCharts,
      iconColor: '#F56C6C',
      iconBg: 'bg-red-100'
    }
  ]
})

// 销售趋势图配置
const salesChartOption = computed(() => ({
  title: {
    text: '近30天销售趋势',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>销售额: ¥{c}'
  },
  xAxis: {
    type: 'category',
    data: dashboardData.value?.sales_trend.map(item => item.date) || []
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '¥{value}'
    }
  },
  series: [
    {
      data: dashboardData.value?.sales_trend.map(item => item.amount) || [],
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ]
        }
      }
    }
  ]
}))

// 代理商业绩饼图配置
const performanceChartOption = computed(() => ({
  title: {
    text: '代理商业绩分布',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a}<br/>{b}: ¥{c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '销售额',
      type: 'pie',
      radius: '50%',
      data: dashboardData.value?.agent_performance.map(item => ({
        value: item.sales,
        name: item.agent_name
      })) || [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 获取仪表盘数据
async function fetchDashboardData() {
  try {
    loading.value = true
    const response = await getDashboardStats()
    if (response.code === 0) {
      dashboardData.value = response.data
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新销售图表
async function refreshSalesChart() {
  chartsLoading.value = true
  await fetchDashboardData()
  chartsLoading.value = false
}

// 刷新业绩图表
async function refreshPerformanceChart() {
  chartsLoading.value = true
  await fetchDashboardData()
  chartsLoading.value = false
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  min-height: calc(100vh - 120px);
}
</style>
