<template>
  <div class="h-full flex items-center justify-between px-6">
    <!-- 左侧：面包屑导航 -->
    <div class="flex items-center space-x-4">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item v-if="breadcrumbs.length > 0">
          {{ breadcrumbs[0] }}
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="breadcrumbs.length > 1">
          {{ breadcrumbs[1] }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧：用户信息和操作 -->
    <div class="flex items-center space-x-4">
      <!-- 刷新按钮 -->
      <el-tooltip content="刷新页面" placement="bottom">
        <el-button 
          :icon="Refresh" 
          circle 
          size="small"
          @click="handleRefresh"
        />
      </el-tooltip>

      <!-- 全屏切换 -->
      <el-tooltip content="全屏切换" placement="bottom">
        <el-button 
          :icon="isFullscreen ? 'FullScreen' : 'FullScreen'" 
          circle 
          size="small"
          @click="toggleFullscreen"
        />
      </el-tooltip>

      <!-- 用户信息下拉菜单 -->
      <el-dropdown @command="handleCommand" trigger="click">
        <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
          <el-avatar :size="32" :icon="UserFilled" />
          <div class="hidden md:block">
            <div class="text-sm font-medium text-gray-900">
              {{ authStore.userName }}
            </div>
            <div class="text-xs text-gray-500">
              {{ roleText }}
            </div>
          </div>
          <el-icon class="text-gray-400">
            <ArrowDown />
          </el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile" :icon="User">
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="changePassword" :icon="Lock">
              修改密码
            </el-dropdown-item>
            <el-dropdown-item divided command="logout" :icon="SwitchButton">
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      :before-close="handleClosePasswordDialog"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入原密码"
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosePasswordDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="passwordLoading"
            @click="handleChangePassword"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/store/auth'
import { useFullscreen } from '@vueuse/core'
import {
  Refresh,
  FullScreen,
  UserFilled,
  ArrowDown,
  User,
  Lock,
  SwitchButton
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 全屏功能
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen()

// 修改密码相关
const showPasswordDialog = ref(false)
const passwordLoading = ref(false)
const passwordFormRef = ref<FormInstance>()

interface PasswordForm {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

const passwordForm = reactive<PasswordForm>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const title = route.meta.title as string
  if (!title || title === '仪表盘') return []
  
  const parts = title.split(' - ')
  return parts
})

// 角色文本
const roleText = computed(() => {
  return authStore.isSuper ? '超级管理员' : '代理商'
})

// 密码验证规则
const passwordRules: FormRules<PasswordForm> = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 刷新页面
function handleRefresh() {
  window.location.reload()
}

// 下拉菜单命令处理
async function handleCommand(command: string) {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'changePassword':
      showPasswordDialog.value = true
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 退出登录
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    // 用户取消操作
  }
}

// 修改密码
async function handleChangePassword() {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    await authStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword
    )
    
    ElMessage.success('密码修改成功')
    handleClosePasswordDialog()
  } catch (error: any) {
    ElMessage.error(error.message || '密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 关闭密码对话框
function handleClosePasswordDialog() {
  showPasswordDialog.value = false
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.resetFields()
}
</script>

<style scoped>
.el-breadcrumb {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
