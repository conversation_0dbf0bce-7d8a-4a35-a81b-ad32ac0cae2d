<template>
  <div class="coupon-batch-list">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">优惠券批次管理</h1>
        <p class="text-gray-600 mt-1">管理优惠券批次的创建和分配</p>
      </div>
      <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
        创建批次
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索批次名称"
            :prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="flex gap-2">
          <el-select v-model="searchForm.tierId" placeholder="优惠券档次" clearable style="width: 150px">
            <el-option
              v-for="tier in couponTiers"
              :key="tier.id"
              :label="tier.name"
              :value="tier.id"
            />
          </el-select>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 批次列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <el-table
        v-loading="loading"
        :data="batchList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="name" label="批次名称" min-width="200">
          <template #default="{ row }">
            <div class="font-medium">{{ row.name }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="tier" label="优惠券档次" width="120">
          <template #default="{ row }">
            <el-tag type="success">{{ row.tier?.name }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="发行情况" width="150">
          <template #default="{ row }">
            <div class="text-sm">
              <div>总量: {{ row.total_quantity }}</div>
              <div>已发: {{ row.issued_quantity }}</div>
              <div class="text-blue-600">
                剩余: {{ row.total_quantity - row.issued_quantity }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="有效期" width="200">
          <template #default="{ row }">
            <div class="text-sm">
              <div>{{ formatDate(row.valid_from) }}</div>
              <div>至</div>
              <div>{{ formatDate(row.valid_to) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              link
              :icon="Gift"
              @click="handleAssign(row)"
              :disabled="row.total_quantity <= row.issued_quantity"
            >
              分配
            </el-button>
            <el-button
              size="small"
              type="info"
              link
              :icon="View"
              @click="handleViewInstances(row)"
            >
              查看实例
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建批次对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建优惠券批次"
      width="500px"
      :before-close="handleCloseCreateDialog"
    >
      <el-form
        ref="batchFormRef"
        :model="batchForm"
        :rules="batchRules"
        label-width="100px"
      >
        <el-form-item label="批次名称" prop="name">
          <el-input
            v-model="batchForm.name"
            placeholder="请输入批次名称"
          />
        </el-form-item>
        
        <el-form-item label="优惠券档次" prop="tier_id">
          <el-select v-model="batchForm.tier_id" placeholder="请选择优惠券档次" style="width: 100%">
            <el-option
              v-for="tier in couponTiers"
              :key="tier.id"
              :label="`${tier.name} (¥${tier.value})`"
              :value="tier.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="发行数量" prop="total_quantity">
          <el-input-number
            v-model="batchForm.total_quantity"
            :min="1"
            :max="10000"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="有效期" prop="validPeriod">
          <el-date-picker
            v-model="batchForm.validPeriod"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseCreateDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="submitLoading"
            @click="handleCreateBatch"
          >
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配优惠券对话框 -->
    <el-dialog
      v-model="showAssignDialog"
      title="分配优惠券"
      width="400px"
      :before-close="handleCloseAssignDialog"
    >
      <el-form
        ref="assignFormRef"
        :model="assignForm"
        :rules="assignRules"
        label-width="100px"
      >
        <el-form-item label="批次信息">
          <div class="text-sm text-gray-600">
            <div>批次: {{ selectedBatch?.name }}</div>
            <div>档次: {{ selectedBatch?.tier?.name }}</div>
            <div>剩余: {{ selectedBatch ? selectedBatch.total_quantity - selectedBatch.issued_quantity : 0 }}</div>
          </div>
        </el-form-item>
        
        <el-form-item label="选择代理商" prop="agent_id">
          <el-select v-model="assignForm.agent_id" placeholder="请选择代理商" style="width: 100%">
            <el-option
              v-for="agent in agentList"
              :key="agent.id"
              :label="agent.nickname || agent.username"
              :value="agent.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分配数量" prop="quantity">
          <el-input-number
            v-model="assignForm.quantity"
            :min="1"
            :max="selectedBatch ? selectedBatch.total_quantity - selectedBatch.issued_quantity : 0"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseAssignDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="assignLoading"
            @click="handleAssignCoupons"
          >
            分配
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Gift,
  View
} from '@element-plus/icons-vue'
import { 
  getCouponBatches, 
  createCouponBatch, 
  getCouponTiers,
  assignCoupons,
  getAgents
} from '@/api/admin'
import type { 
  CouponBatch, 
  CouponTier, 
  CreateCouponBatchRequest,
  AssignCouponRequest,
  Agent
} from '@/types/admin'
import type { QueryParams } from '@/types/api'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const assignLoading = ref(false)
const showCreateDialog = ref(false)
const showAssignDialog = ref(false)
const batchList = ref<CouponBatch[]>([])
const couponTiers = ref<CouponTier[]>([])
const agentList = ref<Agent[]>([])
const selectedBatch = ref<CouponBatch | null>(null)
const batchFormRef = ref<FormInstance>()
const assignFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  tierId: undefined as number | undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 批次表单
interface BatchForm {
  name: string
  tier_id: number | undefined
  total_quantity: number
  validPeriod: [string, string] | null
}

const batchForm = reactive<BatchForm>({
  name: '',
  tier_id: undefined,
  total_quantity: 100,
  validPeriod: null
})

// 分配表单
interface AssignForm {
  agent_id: number | undefined
  quantity: number
}

const assignForm = reactive<AssignForm>({
  agent_id: undefined,
  quantity: 1
})

// 批次表单验证规则
const batchRules: FormRules<BatchForm> = {
  name: [
    { required: true, message: '请输入批次名称', trigger: 'blur' }
  ],
  tier_id: [
    { required: true, message: '请选择优惠券档次', trigger: 'change' }
  ],
  total_quantity: [
    { required: true, message: '请输入发行数量', trigger: 'blur' }
  ],
  validPeriod: [
    { required: true, message: '请选择有效期', trigger: 'change' }
  ]
}

// 分配表单验证规则
const assignRules: FormRules<AssignForm> = {
  agent_id: [
    { required: true, message: '请选择代理商', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入分配数量', trigger: 'blur' }
  ]
}

// 获取优惠券档次
async function fetchCouponTiers() {
  try {
    const response = await getCouponTiers()
    if (response.code === 0) {
      couponTiers.value = response.data
    }
  } catch (error) {
    console.error('获取优惠券档次失败:', error)
  }
}

// 获取代理商列表
async function fetchAgents() {
  try {
    const response = await getAgents({ page: 1, size: 1000 })
    if (response.code === 0) {
      agentList.value = response.data.items.filter(agent => agent.status)
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
  }
}

// 获取批次列表
async function fetchBatches() {
  try {
    loading.value = true
    const params: QueryParams = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      filters: searchForm.tierId ? { tier_id: searchForm.tierId } : undefined
    }
    
    const response = await getCouponBatches(params)
    if (response.code === 0) {
      batchList.value = response.data.items
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取批次列表失败:', error)
    ElMessage.error('获取批次列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  fetchBatches()
}

// 重置搜索
function handleReset() {
  searchForm.keyword = ''
  searchForm.tierId = undefined
  pagination.page = 1
  fetchBatches()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchBatches()
}

// 当前页变化
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchBatches()
}

// 创建批次
async function handleCreateBatch() {
  if (!batchFormRef.value) return
  
  try {
    await batchFormRef.value.validate()
    submitLoading.value = true
    
    if (!batchForm.validPeriod) {
      ElMessage.error('请选择有效期')
      return
    }
    
    const createData: CreateCouponBatchRequest = {
      name: batchForm.name,
      tier_id: batchForm.tier_id!,
      total_quantity: batchForm.total_quantity,
      valid_from: batchForm.validPeriod[0],
      valid_to: batchForm.validPeriod[1]
    }
    
    const response = await createCouponBatch(createData)
    if (response.code === 0) {
      ElMessage.success('创建成功')
      handleCloseCreateDialog()
      fetchBatches()
    }
  } catch (error: any) {
    ElMessage.error(error.message || '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 分配优惠券
function handleAssign(batch: CouponBatch) {
  selectedBatch.value = batch
  assignForm.agent_id = undefined
  assignForm.quantity = 1
  showAssignDialog.value = true
}

// 执行分配
async function handleAssignCoupons() {
  if (!assignFormRef.value || !selectedBatch.value) return
  
  try {
    await assignFormRef.value.validate()
    assignLoading.value = true
    
    const assignData: AssignCouponRequest = {
      batch_id: selectedBatch.value.id,
      agent_id: assignForm.agent_id!,
      quantity: assignForm.quantity
    }
    
    const response = await assignCoupons(assignData)
    if (response.code === 0) {
      ElMessage.success('分配成功')
      handleCloseAssignDialog()
      fetchBatches()
    }
  } catch (error: any) {
    ElMessage.error(error.message || '分配失败')
  } finally {
    assignLoading.value = false
  }
}

// 查看实例
function handleViewInstances(batch: CouponBatch) {
  router.push({
    path: '/coupons/instances',
    query: { batch_id: batch.id }
  })
}

// 关闭创建对话框
function handleCloseCreateDialog() {
  showCreateDialog.value = false
  batchForm.name = ''
  batchForm.tier_id = undefined
  batchForm.total_quantity = 100
  batchForm.validPeriod = null
  batchFormRef.value?.resetFields()
}

// 关闭分配对话框
function handleCloseAssignDialog() {
  showAssignDialog.value = false
  selectedBatch.value = null
  assignForm.agent_id = undefined
  assignForm.quantity = 1
  assignFormRef.value?.resetFields()
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD')
}

// 格式化日期时间
function formatDateTime(date: string) {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCouponTiers()
  fetchAgents()
  fetchBatches()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
