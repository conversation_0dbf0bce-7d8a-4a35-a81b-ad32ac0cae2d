<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <!-- 错误图标 -->
      <div class="mx-auto h-24 w-24 bg-blue-100 rounded-full flex items-center justify-center mb-8">
        <el-icon :size="48" color="#409EFF">
          <QuestionFilled />
        </el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="mb-8">
        <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">页面不存在</h2>
        <p class="text-gray-600">
          抱歉，您访问的页面不存在或已被移除。请检查URL是否正确。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-4">
        <el-button type="primary" size="large" @click="goBack">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        
        <div>
          <el-button type="default" @click="goHome">
            <el-icon class="mr-2"><HomeFilled /></el-icon>
            返回首页
          </el-button>
        </div>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="text-sm font-medium text-blue-800 mb-2">可能的原因</h3>
        <ul class="text-sm text-blue-600 text-left space-y-1">
          <li>• URL地址输入错误</li>
          <li>• 页面已被删除或移动</li>
          <li>• 链接已过期</li>
          <li>• 您没有访问权限</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { QuestionFilled, ArrowLeft, HomeFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 返回上一页
function goBack() {
  router.go(-1)
}

// 返回首页
function goHome() {
  router.push('/dashboard')
}
</script>
