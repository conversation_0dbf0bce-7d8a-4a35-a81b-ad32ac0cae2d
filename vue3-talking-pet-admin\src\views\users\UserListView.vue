<template>
  <div class="user-list">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
      <p class="text-gray-600 mt-1">管理所有C端用户信息</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索用户昵称或手机号"
          :prefix-icon="Search"
          clearable
          @keyup.enter="handleSearch"
        />
        
        <el-select 
          v-if="authStore.isSuper"
          v-model="searchForm.agentId" 
          placeholder="选择代理商" 
          clearable
        >
          <el-option
            v-for="agent in agentList"
            :key="agent.id"
            :label="agent.nickname || agent.username"
            :value="agent.id"
          />
        </el-select>
        
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="注册开始日期"
          end-placeholder="注册结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
        
        <div class="flex gap-2">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="用户ID" width="100" sortable="custom" />
        
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <el-avatar 
                :size="40" 
                :src="row.avatar" 
                :icon="UserFilled"
                class="flex-shrink-0"
              />
              <div>
                <div class="font-medium">{{ row.nickname || '未设置昵称' }}</div>
                <div class="text-sm text-gray-500">{{ row.phone || '未绑定手机' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          v-if="authStore.isSuper"
          prop="agent" 
          label="来源代理商" 
          min-width="150"
        >
          <template #default="{ row }">
            <div v-if="row.agent">
              <div class="font-medium">{{ row.agent.nickname || row.agent.username }}</div>
              <div class="text-sm text-gray-500">ID: {{ row.agent.id }}</div>
            </div>
            <span v-else class="text-gray-400">直接注册</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_orders" label="订单数" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="text-blue-600 font-medium">{{ row.total_orders || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_amount" label="消费金额" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="text-green-600 font-medium">
              ¥{{ (row.total_amount || 0).toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="registered_at" label="注册时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.registered_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              link
              :icon="View"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="用户详情"
      width="600px"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedUser" class="space-y-4">
        <!-- 基本信息 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-medium mb-3">基本信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm text-gray-600">用户ID</label>
              <p class="font-medium">{{ selectedUser.id }}</p>
            </div>
            <div>
              <label class="text-sm text-gray-600">昵称</label>
              <p class="font-medium">{{ selectedUser.nickname || '未设置' }}</p>
            </div>
            <div>
              <label class="text-sm text-gray-600">手机号</label>
              <p class="font-medium">{{ selectedUser.phone || '未绑定' }}</p>
            </div>
            <div>
              <label class="text-sm text-gray-600">注册时间</label>
              <p class="font-medium">{{ formatDateTime(selectedUser.registered_at) }}</p>
            </div>
          </div>
        </div>

        <!-- 代理商信息 -->
        <div v-if="selectedUser.agent" class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-medium mb-3">代理商信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm text-gray-600">代理商昵称</label>
              <p class="font-medium">{{ selectedUser.agent.nickname || selectedUser.agent.username }}</p>
            </div>
            <div>
              <label class="text-sm text-gray-600">代理商ID</label>
              <p class="font-medium">{{ selectedUser.agent.id }}</p>
            </div>
          </div>
        </div>

        <!-- 消费统计 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-medium mb-3">消费统计</h4>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm text-gray-600">订单数量</label>
              <p class="font-medium text-blue-600">{{ selectedUser.total_orders || 0 }}</p>
            </div>
            <div>
              <label class="text-sm text-gray-600">消费金额</label>
              <p class="font-medium text-green-600">¥{{ (selectedUser.total_amount || 0).toLocaleString() }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDetail">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Download,
  View,
  UserFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import { getEndUsers, getAgents, exportData } from '@/api/admin'
import type { EndUser, Agent } from '@/types/admin'
import type { QueryParams } from '@/types/api'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const userList = ref<EndUser[]>([])
const agentList = ref<Agent[]>([])
const selectedUser = ref<EndUser | null>(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  agentId: undefined as number | undefined,
  dateRange: null as [string, string] | null
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取代理商列表
async function fetchAgents() {
  if (!authStore.isSuper) return
  
  try {
    const response = await getAgents({ page: 1, size: 1000 })
    if (response.code === 0) {
      agentList.value = response.data.items
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
  }
}

// 获取用户列表
async function fetchUsers() {
  try {
    loading.value = true
    const params: QueryParams = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      filters: {
        ...(searchForm.agentId && { agent_id: searchForm.agentId }),
        ...(searchForm.dateRange && {
          start_date: searchForm.dateRange[0],
          end_date: searchForm.dateRange[1]
        })
      }
    }
    
    const response = await getEndUsers(params)
    if (response.code === 0) {
      userList.value = response.data.items
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  fetchUsers()
}

// 重置搜索
function handleReset() {
  searchForm.keyword = ''
  searchForm.agentId = undefined
  searchForm.dateRange = null
  pagination.page = 1
  fetchUsers()
}

// 排序变化
function handleSortChange({ prop, order }: any) {
  console.log('Sort change:', prop, order)
  fetchUsers()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchUsers()
}

// 当前页变化
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchUsers()
}

// 查看详情
function handleViewDetail(user: EndUser) {
  selectedUser.value = user
  showDetailDialog.value = true
}

// 关闭详情
function handleCloseDetail() {
  showDetailDialog.value = false
  selectedUser.value = null
}

// 导出数据
async function handleExport() {
  try {
    ElMessage.info('正在导出数据，请稍候...')
    const blob = await exportData('users', {
      keyword: searchForm.keyword,
      agent_id: searchForm.agentId,
      start_date: searchForm.dateRange?.[0],
      end_date: searchForm.dateRange?.[1]
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `users_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 格式化日期时间
function formatDateTime(date: string) {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAgents()
  fetchUsers()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
