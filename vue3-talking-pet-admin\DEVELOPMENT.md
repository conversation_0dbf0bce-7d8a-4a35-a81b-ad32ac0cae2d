# 开发指南

本文档提供了AI玩偶后台管理系统的详细开发指南。

## 🏁 快速开始

### 1. 环境准备
确保您的开发环境满足以下要求：
- Node.js >= 16.0.0
- npm >= 8.0.0
- Git

### 2. 项目克隆和安装
```bash
# 克隆项目
git clone <repository-url>
cd vue3-talking-pet-admin

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 后端API配置
在 `.env.development` 文件中配置后端API地址：
```env
VITE_API_BASE_URL=http://localhost:8000
```

## 🏗️ 架构设计

### 目录结构说明

```
src/
├── api/                    # API接口层
│   ├── index.ts           # 基础配置和拦截器
│   ├── auth.ts            # 认证相关API
│   └── admin.ts           # 管理后台API
├── components/             # 组件目录
│   ├── admin/             # 管理后台专用组件
│   │   ├── AdminHeader.vue
│   │   └── AdminSidebar.vue
│   ├── common/            # 通用组件
│   │   ├── PageHeader.vue
│   │   ├── EmptyState.vue
│   │   └── ConfirmDialog.vue
│   └── TheLayout.vue      # 主布局组件
├── composables/           # 组合式函数
│   ├── usePermission.ts   # 权限检查
│   └── useTable.ts        # 表格操作
├── router/                # 路由配置
│   ├── index.ts           # 路由定义
│   └── guards.ts          # 路由守卫
├── store/                 # 状态管理
│   ├── auth.ts            # 认证状态
│   ├── counter.ts         # 示例store
│   └── user.ts            # 用户信息
├── types/                 # TypeScript类型定义
│   ├── api.ts             # API相关类型
│   ├── auth.ts            # 认证相关类型
│   └── admin.ts           # 管理后台类型
├── utils/                 # 工具函数
│   └── index.ts           # 通用工具函数
└── views/                 # 页面组件
    ├── auth/              # 认证页面
    ├── dashboard/         # 仪表盘
    ├── agents/            # 代理商管理
    ├── coupons/           # 优惠券管理
    ├── users/             # 用户管理
    ├── tools/             # 系统工具
    ├── profile/           # 个人中心
    └── error/             # 错误页面
```

### 核心设计原则

1. **单一职责原则**: 每个组件和函数都有明确的职责
2. **组合优于继承**: 使用Vue 3 Composition API
3. **类型安全**: 全面使用TypeScript
4. **权限控制**: 基于RBAC的权限管理
5. **响应式设计**: 移动端友好

## 🔧 开发规范

### 代码风格

#### Vue组件
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/component'

// 接口定义
interface Props {
  title: string
  visible?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
}

// Props和Emits
const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const formData = reactive({
  name: '',
  email: ''
})

// 计算属性
const isValid = computed(() => {
  return formData.name && formData.email
})

// 方法
function handleSubmit() {
  // 处理逻辑
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
.component-name {
  /* 样式 */
}
</style>
```

#### TypeScript类型定义
```typescript
// 基础类型
export interface User {
  id: number
  username: string
  email: string
  role: UserRole
}

// 联合类型
export type UserRole = 'SuperAdmin' | 'Agent'

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 函数类型
export type EventHandler = (event: Event) => void
```

#### Composable函数
```typescript
import { ref, computed } from 'vue'

export function useCounter(initialValue = 0) {
  const count = ref(initialValue)
  
  const doubleCount = computed(() => count.value * 2)
  
  function increment() {
    count.value++
  }
  
  function decrement() {
    count.value--
  }
  
  function reset() {
    count.value = initialValue
  }
  
  return {
    count,
    doubleCount,
    increment,
    decrement,
    reset
  }
}
```

### 命名规范

- **组件**: PascalCase (例: `UserList.vue`)
- **文件**: kebab-case (例: `user-list.vue`)
- **变量/函数**: camelCase (例: `userName`, `getUserInfo`)
- **常量**: UPPER_SNAKE_CASE (例: `API_BASE_URL`)
- **类型/接口**: PascalCase (例: `UserInfo`, `ApiResponse`)

### Git提交规范

使用语义化提交信息：

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(auth): 添加JWT认证功能

- 实现登录接口
- 添加token存储
- 实现路由守卫

Closes #123
```

## 🔐 权限系统

### 权限检查
使用 `usePermission` composable进行权限检查：

```vue
<script setup lang="ts">
import { usePermission } from '@/composables/usePermission'

const { isSuper, canManageAgents } = usePermission()
</script>

<template>
  <div>
    <el-button v-if="canManageAgents" type="primary">
      管理代理商
    </el-button>
  </div>
</template>
```

### 路由权限
在路由配置中定义权限：

```typescript
{
  path: '/agents',
  name: 'Agents',
  component: () => import('@/views/agents/AgentListView.vue'),
  meta: {
    title: '代理商管理',
    requiresAuth: true,
    roles: ['SuperAdmin']
  }
}
```

## 📊 状态管理

### Pinia Store示例
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const users = ref<User[]>([])
  const loading = ref(false)
  
  // 计算属性
  const userCount = computed(() => users.value.length)
  
  // 方法
  async function fetchUsers() {
    loading.value = true
    try {
      const response = await getUserList()
      users.value = response.data
    } catch (error) {
      console.error('获取用户列表失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  return {
    users,
    loading,
    userCount,
    fetchUsers
  }
})
```

## 🎨 样式开发

### Tailwind CSS使用
优先使用Tailwind CSS工具类：

```vue
<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">标题</h1>
    <p class="text-gray-600">内容</p>
  </div>
</template>
```

### Element Plus主题定制
在需要时可以覆盖Element Plus样式：

```vue
<style scoped>
:deep(.el-button) {
  border-radius: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}
</style>
```

## 🧪 测试

### 单元测试
使用Vitest进行单元测试：

```typescript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  it('renders user information correctly', () => {
    const wrapper = mount(UserCard, {
      props: {
        user: {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>'
        }
      }
    })
    
    expect(wrapper.text()).toContain('John Doe')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
})
```

## 🚀 部署

### 构建生产版本
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

### 环境变量配置
生产环境需要配置正确的API地址：

```env
# .env.production
VITE_API_BASE_URL=https://api.your-domain.com
VITE_APP_TITLE=AI玩偶后台管理系统
VITE_APP_VERSION=1.0.0
```

## 🐛 调试技巧

### Vue DevTools
安装Vue DevTools浏览器扩展进行调试。

### 网络请求调试
在浏览器开发者工具的Network面板中查看API请求。

### 状态调试
使用Pinia DevTools查看状态变化。

## 📚 学习资源

- [Vue 3 官方文档](https://vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Element Plus 官方文档](https://element-plus.org/)
- [Tailwind CSS 官方文档](https://tailwindcss.com/)
- [Pinia 官方文档](https://pinia.vuejs.org/)

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

---

如有问题，请查看项目文档或联系开发团队。
