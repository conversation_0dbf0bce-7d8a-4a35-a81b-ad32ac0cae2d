<template>
  <div class="h-full flex flex-col">
    <!-- Logo 区域 -->
    <div class="h-16 flex items-center justify-center border-b border-gray-200">
      <div class="flex items-center space-x-2">
        <el-icon :size="24" color="#409EFF">
          <Management />
        </el-icon>
        <span class="text-lg font-bold text-gray-800">AI玩偶管理</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="flex-1 py-4">
      <el-menu
        :default-active="activeMenu"
        class="border-none"
        router
        @select="handleMenuSelect"
      >
        <!-- 仪表盘 -->
        <el-menu-item index="/dashboard" class="menu-item">
          <el-icon><DataBoard /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>

        <!-- 代理商管理 (仅超级管理员) -->
        <el-menu-item 
          v-if="authStore.isSuper" 
          index="/agents" 
          class="menu-item"
        >
          <el-icon><UserFilled /></el-icon>
          <span>代理商管理</span>
        </el-menu-item>

        <!-- 优惠券管理 -->
        <el-sub-menu index="coupons" class="menu-item">
          <template #title>
            <el-icon><Tickets /></el-icon>
            <span>优惠券管理</span>
          </template>
          
          <el-menu-item 
            v-if="authStore.isSuper" 
            index="/coupons/batches"
            class="sub-menu-item"
          >
            <el-icon><Collection /></el-icon>
            <span>批次管理</span>
          </el-menu-item>
          
          <el-menu-item 
            index="/coupons/instances"
            class="sub-menu-item"
          >
            <el-icon><Ticket /></el-icon>
            <span>优惠券实例</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 用户管理 -->
        <el-menu-item index="/users" class="menu-item">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </el-menu-item>

        <!-- 系统工具 (仅超级管理员) -->
        <el-sub-menu v-if="authStore.isSuper" index="tools" class="menu-item">
          <template #title>
            <el-icon><Operation /></el-icon>
            <span>系统工具</span>
          </template>
          
          <el-menu-item 
            index="/tools/coupon-simulator"
            class="sub-menu-item"
          >
            <el-icon><Cpu /></el-icon>
            <span>优惠券模拟器</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 个人中心 -->
        <el-menu-item index="/profile" class="menu-item">
          <el-icon><Setting /></el-icon>
          <span>个人中心</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 底部信息 -->
    <div class="p-4 border-t border-gray-200">
      <div class="text-xs text-gray-500 text-center">
        <p>版本 v1.0.0</p>
        <p class="mt-1">© 2024 AI玩偶</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import {
  Management,
  DataBoard,
  UserFilled,
  Tickets,
  Collection,
  Ticket,
  User,
  Operation,
  Cpu,
  Setting
} from '@element-plus/icons-vue'

const route = useRoute()
const authStore = useAuthStore()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  
  // 处理子菜单的激活状态
  if (path.startsWith('/coupons/')) {
    return path
  }
  if (path.startsWith('/tools/')) {
    return path
  }
  
  return path
})

// 菜单选择处理
function handleMenuSelect(index: string) {
  console.log('Selected menu:', index)
}
</script>

<style scoped>
.menu-item {
  margin: 4px 12px;
  border-radius: 8px;
}

.sub-menu-item {
  margin: 2px 8px;
  border-radius: 6px;
}

.el-menu {
  background-color: transparent;
}

.el-menu-item:hover,
.el-sub-menu__title:hover {
  background-color: #f0f9ff !important;
  color: #1d4ed8 !important;
}

.el-menu-item.is-active {
  background-color: #dbeafe !important;
  color: #1d4ed8 !important;
  font-weight: 600;
}

.el-sub-menu.is-active > .el-sub-menu__title {
  color: #1d4ed8 !important;
  font-weight: 600;
}
</style>
