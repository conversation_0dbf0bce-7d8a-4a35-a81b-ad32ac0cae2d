# AI玩偶后台管理系统

基于 Vue 3 + TypeScript + Element Plus 开发的现代化后台管理系统。

## 🚀 功能特性

### 核心功能
- 🔐 **统一认证系统** - JWT Token 认证，支持超级管理员和代理商两种角色
- 👥 **角色权限管理** - 基于 RBAC 的权限控制，数据安全隔离
- 📊 **数据可视化** - ECharts 图表展示，实时数据监控
- 🎫 **优惠券系统** - 完整的优惠券生命周期管理
- 👤 **用户管理** - C端用户信息管理和统计
- 🛠️ **系统工具** - 优惠券使用模拟器等实用工具

### 技术特性
- ⚡ **现代化技术栈** - Vue 3 Composition API + TypeScript
- 🎨 **优雅的UI** - Element Plus + Tailwind CSS
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🔄 **状态管理** - Pinia 状态管理
- 🚦 **路由守卫** - 基于角色的路由权限控制
- 📦 **组件化开发** - 高度可复用的组件设计

## 🛠️ 技术栈

- **框架**: Vue 3.5.13
- **语言**: TypeScript 5.6.3
- **构建工具**: Vite 3.2.11
- **UI组件库**: Element Plus 2.10.4
- **样式框架**: Tailwind CSS 3.4.17
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4.5.0
- **图表库**: ECharts 5.6.0
- **HTTP客户端**: Axios 1.10.0
- **日期处理**: Day.js 1.11.13
- **工具库**: VueUse 13.5.0

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 🏗️ 项目结构

```
src/
├── api/                    # API 接口层
│   ├── index.ts           # API 基础配置
│   ├── auth.ts            # 认证相关 API
│   └── admin.ts           # 管理后台 API
├── components/             # 组件
│   ├── admin/             # 管理后台专用组件
│   ├── common/            # 通用组件
│   └── TheLayout.vue      # 主布局组件
├── composables/           # 组合式函数
├── router/                # 路由配置
├── store/                 # 状态管理
├── types/                 # TypeScript 类型定义
├── utils/                 # 工具函数
├── views/                 # 页面组件
├── App.vue                # 根组件
└── main.ts                # 应用入口
```

## 🔐 用户角色和权限

### 超级管理员 (SuperAdmin)
- ✅ 仪表盘 - 全局数据统计
- ✅ 代理商管理 - 创建、编辑、删除代理商
- ✅ 优惠券批次管理 - 创建批次、分配优惠券
- ✅ 优惠券实例管理 - 查看所有优惠券
- ✅ 用户管理 - 查看所有用户
- ✅ 系统工具 - 优惠券模拟器
- ✅ 个人中心 - 修改个人信息

### 代理商 (Agent)
- ✅ 仪表盘 - 个人业绩统计
- ❌ 代理商管理 - 无权限
- ❌ 优惠券批次管理 - 无权限
- ✅ 优惠券实例管理 - 仅查看自己的优惠券
- ✅ 用户管理 - 仅查看自己的用户
- ❌ 系统工具 - 无权限
- ✅ 个人中心 - 修改个人信息

## 🌐 环境配置

### 开发环境 (.env.development)
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=AI玩偶后台管理系统 (开发环境)
VITE_APP_VERSION=1.0.0-dev
```

### 生产环境 (.env.production)
```env
VITE_API_BASE_URL=https://api.your-domain.com
VITE_APP_TITLE=AI玩偶后台管理系统
VITE_APP_VERSION=1.0.0
```

## 📝 开发规范

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

© 2024 AI玩偶后台管理系统. 保留所有权利.
