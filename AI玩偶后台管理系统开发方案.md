# AI玩偶后台管理系统开发方案

## 📋 项目概述

基于现有Vue3+FastAPI项目模板，开发统一的AI玩偶小程序后台管理系统。系统支持超级管理员和代理商两种角色，通过RBAC权限控制实现功能和数据隔离。

### 现有技术栈分析
**前端模板 (vue3-talking-pet-admin)**:
- Vue 3.5.13 + TypeScript + Vite 3.2.11
- Pinia 3.0.2 状态管理
- Vue Router 4 路由管理
- Tailwind CSS 3.4.17 样式框架
- Element Plus 2.10.4 UI组件库 (已安装)
- ECharts 5.6.0 图表库 (已安装)
- Axios 1.10.0 HTTP客户端 (已安装)

**后端模板 (fastapi-talking-pet-admin)**:
- Python 3.10+ + FastAPI 0.104.1
- SQLAlchemy 2.0.23 ORM
- PyMySQL 1.1.0 数据库驱动
- Pydantic 2.5.0 数据验证
- Uvicorn 0.24.0 ASGI服务器

### 核心开发原则
1. **完全保持现有模板架构** - 不破坏任何现有结构，只进行扩展和改造
2. **渐进式开发** - 基于现有代码逐步添加新功能
3. **模块化设计** - 新功能以独立模块形式集成
4. **数据安全隔离** - 严格的角色权限控制

### 项目结构扩展计划
**前端扩展 (基于现有vue3-talking-pet-admin)**:
```
vue3-talking-pet-admin/
├── src/
│   ├── components/          # 保持现有，扩展新组件
│   │   ├── TheLayout.vue   # 改造：后台管理布局
│   │   ├── TheNavbar.vue   # 改造：后台导航栏
│   │   └── admin/          # 新增：管理后台专用组件
│   ├── views/              # 保持现有，添加新页面
│   │   ├── HomeView.vue    # 保留：可作为登录后首页
│   │   ├── auth/           # 新增：认证相关页面
│   │   ├── dashboard/      # 新增：仪表盘
│   │   ├── agents/         # 新增：代理商管理
│   │   ├── coupons/        # 新增：优惠券管理
│   │   ├── users/          # 新增：用户管理
│   │   └── tools/          # 新增：系统工具
│   ├── store/              # 扩展现有store
│   │   ├── counter.ts      # 保留：现有示例store
│   │   ├── user.ts         # 保留：现有用户store
│   │   ├── auth.ts         # 新增：认证状态管理
│   │   └── admin/          # 新增：管理功能状态
│   ├── api/                # 扩展现有api目录
│   │   └── admin/          # 新增：管理后台API
│   ├── router/             # 扩展现有路由
│   │   ├── index.ts        # 改造：添加管理后台路由
│   │   └── guards/         # 新增：路由守卫
│   ├── types/              # 新增：TypeScript类型
│   │   └── admin/          # 管理后台类型定义
│   └── utils/              # 新增：工具函数
```

**后端扩展 (基于现有fastapi-talking-pet-admin)**:
```
fastapi-talking-pet-admin/
├── app/
│   ├── core/               # 保持现有，扩展配置
│   │   ├── config.py       # 扩展：添加JWT等配置
│   │   ├── database.py     # 保持：现有数据库配置
│   │   ├── auth.py         # 新增：JWT认证逻辑
│   │   └── security.py     # 新增：安全相关工具
│   ├── models/             # 扩展现有模型
│   │   ├── user.py         # 保留：现有用户模型
│   │   ├── admin_user.py   # 新增：管理员用户模型
│   │   ├── role.py         # 新增：角色模型
│   │   ├── coupon.py       # 新增：优惠券相关模型
│   │   └── __init__.py     # 更新：导入所有模型
│   ├── api/                # 扩展现有API
│   │   ├── routes.py       # 扩展：注册新路由
│   │   └── endpoints/      # 扩展现有端点
│   │       ├── hello.py    # 保留：现有示例端点
│   │       ├── auth.py     # 新增：认证端点
│   │       ├── admin.py    # 新增：管理员管理
│   │       ├── agents.py   # 新增：代理商管理
│   │       ├── coupons.py  # 新增：优惠券管理
│   │       └── dashboard.py # 新增：仪表盘数据
│   ├── schemas/            # 新增：Pydantic模式
│   │   ├── auth.py         # 认证相关模式
│   │   ├── admin.py        # 管理员模式
│   │   └── coupon.py       # 优惠券模式
│   └── utils/              # 新增：工具函数
├── main.py                 # 保持：现有应用入口
└── requirements.txt        # 扩展：添加新依赖
```

---

## 🚀 前端开发计划 (基于现有模板渐进式开发)

### 阶段一：环境准备和基础架构 (1-2天)

#### 1.1 依赖检查和补充安装
**目标**: 检查现有依赖，补充必要组件

**现有依赖分析**:
- ✅ Element Plus 2.10.4 (已安装)
- ✅ @element-plus/icons-vue 2.3.1 (已安装)
- ✅ Axios 1.10.0 (已安装)
- ✅ @vueuse/core 13.5.0 (已安装)
- ✅ js-cookie 3.0.5 (已安装)
- ✅ @types/js-cookie 3.0.6 (已安装)
- ✅ dayjs 1.11.13 (已安装)
- ✅ echarts 5.6.0 (已安装)
- ✅ vue-echarts 7.0.3 (已安装)

**需要补充的依赖**:
```bash
# JWT处理库
npm install jsonwebtoken @types/jsonwebtoken

# 表单验证增强
npm install @vuelidate/core @vuelidate/validators

# 工具库
npm install lodash-es @types/lodash-es
```

#### 1.2 Element Plus集成优化
**目标**: 优化Element Plus在现有项目中的集成

**任务清单**:
- [ ] 检查main.ts中Element Plus配置
- [ ] 确保图标组件正确注册
- [ ] 配置Element Plus主题色彩
- [ ] 测试组件库功能完整性

#### 1.3 API架构设计 (基于现有结构)
**目标**: 在现有api目录基础上建立管理后台API层

**任务清单**:
- [ ] 扩展 `src/api/` 目录结构
- [ ] 创建 `src/api/index.ts` 统一配置
- [ ] 创建 `src/api/admin/` 管理后台API模块
- [ ] 实现请求拦截器和响应拦截器
- [ ] 设置统一错误处理机制

#### 1.4 类型定义系统
**目标**: 建立完整的TypeScript类型定义

**任务清单**:
- [ ] 扩展 `src/types/` 目录
- [ ] 创建 `src/types/api.ts` API响应类型
- [ ] 创建 `src/types/admin/` 管理后台类型
- [ ] 定义用户角色和权限类型

### 阶段二：认证系统开发 (2-3天)

#### 2.1 状态管理扩展 (基于现有Pinia)
**目标**: 在现有store基础上添加认证状态管理

**现有store分析**:
- ✅ `src/store/counter.ts` - 计数器示例store (保留)
- ✅ `src/store/user.ts` - 用户信息store (可扩展利用)

**任务清单**:
- [ ] 分析现有 `src/store/user.ts` 结构
- [ ] 创建 `src/store/auth.ts` 认证专用store
- [ ] 实现JWT token存储和管理
- [ ] 集成js-cookie进行token持久化
- [ ] 创建权限检查composable函数

#### 2.2 登录页面开发
**目标**: 创建专业的管理后台登录页面

**任务清单**:
- [ ] 创建 `src/views/auth/LoginView.vue`
- [ ] 使用Element Plus构建登录表单
- [ ] 集成现有Tailwind CSS样式系统
- [ ] 实现表单验证 (使用async-validator)
- [ ] 添加登录状态管理和错误处理
- [ ] 实现响应式设计 (复用现有Tailwind配置)

**设计要求**:
- 与现有项目视觉风格保持一致
- 使用Element Plus组件库
- 集成现有Tailwind CSS类
- 支持移动端适配

#### 2.3 路由系统扩展 (基于现有Vue Router)
**目标**: 在现有路由基础上添加认证和权限控制

**现有路由分析**:
- ✅ 基础路由配置 (`src/router/index.ts`)
- ✅ 页面标题设置机制
- ✅ 滚动行为配置

**任务清单**:
- [ ] 扩展现有 `src/router/index.ts` 配置
- [ ] 创建 `src/router/guards/` 目录
- [ ] 实现认证路由守卫
- [ ] 添加角色权限检查
- [ ] 创建403/404错误页面
- [ ] 保持现有路由功能完整性

#### 2.4 布局系统改造 (基于现有组件)
**目标**: 改造现有布局组件为管理后台布局

**现有布局分析**:
- ✅ `src/components/TheLayout.vue` - 主布局组件
- ✅ `src/components/TheNavbar.vue` - 导航栏组件
- ✅ 响应式设计和过渡动画

**任务清单**:
- [ ] 分析现有TheLayout.vue结构
- [ ] 改造为管理后台布局 (保持原有特性)
- [ ] 扩展TheNavbar.vue为后台导航
- [ ] 添加侧边栏菜单组件
- [ ] 实现用户信息显示和退出功能
- [ ] 保持现有过渡动画效果

### 阶段三：核心功能模块开发 (6-8天)

#### 3.1 仪表盘开发 (2天) - 利用现有ECharts集成
**目标**: 创建数据概览仪表盘

**现有资源利用**:
- ✅ ECharts 5.6.0 已安装
- ✅ vue-echarts 7.0.3 已安装
- ✅ 现有Tailwind CSS样式系统

**任务清单**:
- [ ] 创建 `src/views/dashboard/DashboardView.vue`
- [ ] 利用现有ECharts创建KPI卡片组件
- [ ] 实现销售趋势图 (基于vue-echarts)
- [ ] 实现代理商业绩饼图
- [ ] 添加数据刷新功能
- [ ] 实现角色差异化显示
- [ ] 复用现有响应式设计模式

#### 3.2 代理商管理模块 (2天)
**目标**: 完成代理商的增删改查功能

**任务清单**:
- [ ] 创建 `src/views/agents/AgentListView.vue`
- [ ] 使用Element Plus Table组件
- [ ] 实现搜索和分页功能 (Element Plus Pagination)
- [ ] 创建代理商创建/编辑表单 (Element Plus Form)
- [ ] 实现状态切换功能
- [ ] 添加批量操作功能
- [ ] 集成现有Tailwind样式

#### 3.3 优惠券管理模块 (2-3天)
**目标**: 完成优惠券批次和实例管理

**任务清单**:
- [ ] 创建 `src/views/coupons/BatchListView.vue`
- [ ] 创建 `src/views/coupons/InstanceListView.vue`
- [ ] 实现批次创建和编辑功能
- [ ] 创建优惠券分配功能
- [ ] 添加高级筛选功能 (Element Plus Select/DatePicker)
- [ ] 实现批量操作
- [ ] 创建优惠券使用统计页面

#### 3.4 用户管理模块 (1天)
**目标**: 完成C端用户管理功能

**任务清单**:
- [ ] 创建 `src/views/users/UserListView.vue`
- [ ] 实现用户搜索功能
- [ ] 添加用户详情查看 (Element Plus Dialog)
- [ ] 实现用户数据导出功能
- [ ] 添加用户统计功能

### 阶段四：系统工具和优化 (1-2天)

#### 4.1 优惠券模拟器 (1天)
**目标**: 开发测试工具

**任务清单**:
- [ ] 创建 `src/views/tools/CouponSimulatorView.vue`
- [ ] 实现参数选择功能 (Element Plus Select)
- [ ] 添加实时日志显示
- [ ] 实现批量模拟功能

#### 4.2 系统优化和个人中心 (1天)
**目标**: 完善系统功能和用户体验

**任务清单**:
- [ ] 创建 `src/views/profile/ProfileView.vue` 个人中心
- [ ] 实现密码修改功能
- [ ] 添加全局Loading组件
- [ ] 优化移动端适配 (基于现有Tailwind响应式)
- [ ] 添加操作确认弹窗
- [ ] 实现路由懒加载优化

---

## 🔧 后端开发计划 (基于现有FastAPI模板)

### 阶段一：基础架构扩展 (1-2天)

#### 1.1 现有项目分析和扩展
**目标**: 在现有FastAPI项目基础上扩展功能

**现有结构分析**:
- ✅ FastAPI 0.104.1 应用框架
- ✅ SQLAlchemy 2.0.23 ORM配置
- ✅ PyMySQL 1.1.0 数据库驱动
- ✅ Pydantic 2.5.0 数据验证
- ✅ 基础项目结构和配置

**现有文件保持**:
- ✅ `main.py` - 应用入口 (扩展路由注册)
- ✅ `app/core/config.py` - 配置文件 (添加JWT配置)
- ✅ `app/core/database.py` - 数据库配置 (保持不变)
- ✅ `app/models/user.py` - 现有用户模型 (保留)
- ✅ `app/api/routes.py` - 路由配置 (扩展新路由)

#### 1.2 依赖扩展
**目标**: 添加认证和安全相关依赖

**需要添加的依赖**:
```txt
# JWT认证
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 数据库迁移
alembic==1.13.1

# 开发工具
python-multipart==0.0.6
```

#### 1.3 配置文件扩展
**目标**: 在现有config.py基础上添加JWT和安全配置

**任务清单**:
- [ ] 扩展 `app/core/config.py` 添加JWT配置
- [ ] 添加CORS配置优化
- [ ] 配置密码加密参数
- [ ] 设置token过期时间

### 阶段二：数据库模型扩展 (1-2天)

#### 2.1 数据库表结构设计 (基于现有数据库配置)
**目标**: 在现有数据库基础上添加管理后台相关表

**现有数据库分析**:
- ✅ 数据库连接配置完整
- ✅ SQLAlchemy Base类已定义
- ✅ 现有User模型 (可作为C端用户表)

**新增表结构**:
```sql
-- 基于PRD要求的表结构
CREATE TABLE `roles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE,
  `description` VARCHAR(255)
);

CREATE TABLE `admin_users` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) NOT NULL UNIQUE,
  `password_hash` VARCHAR(255) NOT NULL,
  `nickname` VARCHAR(50),
  `role_id` INT UNSIGNED NOT NULL,
  `parent_id` INT UNSIGNED NULL,
  `status` TINYINT(1) NOT NULL DEFAULT 1,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 优惠券相关表 (按PRD设计)
CREATE TABLE `coupon_tiers` (...);
CREATE TABLE `coupon_batches` (...);
CREATE TABLE `coupons` (...);
```

#### 2.2 SQLAlchemy模型创建
**目标**: 创建对应的SQLAlchemy模型

**任务清单**:
- [ ] 创建 `app/models/admin_user.py`
- [ ] 创建 `app/models/role.py`
- [ ] 创建 `app/models/coupon.py` (包含所有优惠券相关模型)
- [ ] 更新 `app/models/__init__.py` 导入新模型
- [ ] 保持现有 `app/models/user.py` 不变

#### 2.3 数据库迁移配置
**目标**: 配置Alembic数据库迁移

**任务清单**:
- [ ] 初始化Alembic配置
- [ ] 创建初始迁移脚本
- [ ] 创建数据初始化脚本 (默认角色和管理员)
- [ ] 测试迁移功能

### 阶段三：认证系统开发 (2天)

#### 3.1 JWT认证实现
**目标**: 实现完整的JWT认证系统

**任务清单**:
- [ ] 创建 `app/core/auth.py` JWT工具函数
- [ ] 创建 `app/core/security.py` 密码加密工具
- [ ] 实现JWT token生成和验证
- [ ] 创建认证依赖函数
- [ ] 实现密码加密和验证

#### 3.2 认证API端点
**目标**: 创建认证相关API

**任务清单**:
- [ ] 创建 `app/api/endpoints/auth.py`
- [ ] 实现登录接口 `/api/v1/admin/auth/login`
- [ ] 实现获取当前用户信息接口 `/api/v1/admin/auth/me`
- [ ] 实现密码修改接口
- [ ] 在 `app/api/routes.py` 中注册认证路由

#### 3.3 权限控制系统
**目标**: 实现RBAC权限控制

**任务清单**:
- [ ] 创建权限检查装饰器
- [ ] 实现角色权限检查函数
- [ ] 创建数据隔离中间件 (代理商只能访问自己的数据)
- [ ] 实现API权限控制

### 阶段四：核心业务API开发 (3-4天)

#### 4.1 管理员和代理商API (1天)
**目标**: 实现用户管理相关API

**任务清单**:
- [ ] 创建 `app/api/endpoints/admin.py`
- [ ] 实现管理员用户CRUD接口
- [ ] 创建代理商管理接口
- [ ] 实现用户状态管理
- [ ] 添加用户搜索和分页功能
- [ ] 实现数据权限隔离

#### 4.2 优惠券系统API (2天)
**目标**: 实现完整的优惠券管理API

**任务清单**:
- [ ] 创建 `app/api/endpoints/coupons.py`
- [ ] 实现优惠券档次管理API
- [ ] 创建优惠券批次CRUD接口
- [ ] 实现优惠券分配逻辑
- [ ] 创建优惠券实例查询接口
- [ ] 实现优惠券使用接口
- [ ] 添加库存管理和统计逻辑

#### 4.3 仪表盘数据API (1天)
**目标**: 提供仪表盘所需的统计数据

**任务清单**:
- [ ] 创建 `app/api/endpoints/dashboard.py`
- [ ] 实现KPI数据接口 (用户数、销售额等)
- [ ] 创建销售趋势数据API
- [ ] 实现代理商业绩统计API
- [ ] 添加实时数据更新接口
- [ ] 实现角色差异化数据返回

#### 4.4 系统工具API (半天)
**目标**: 实现系统工具相关API

**任务清单**:
- [ ] 创建 `app/api/endpoints/tools.py`
- [ ] 实现优惠券模拟器API
- [ ] 创建数据导出接口
- [ ] 添加系统健康检查API

### 阶段五：Pydantic模式定义 (半天)
**目标**: 创建完整的数据验证模式

**任务清单**:
- [ ] 创建 `app/schemas/` 目录
- [ ] 定义认证相关模式 (`auth.py`)
- [ ] 定义管理员模式 (`admin.py`)
- [ ] 定义优惠券模式 (`coupon.py`)
- [ ] 定义仪表盘数据模式 (`dashboard.py`)

### 阶段六：系统集成和优化 (1天)

#### 6.1 前后端集成测试
**目标**: 确保前后端完美对接

**任务清单**:
- [ ] API接口联调测试
- [ ] 验证CORS配置
- [ ] 测试认证流程
- [ ] 验证权限控制
- [ ] 测试数据隔离

#### 6.2 性能优化和文档
**任务清单**:
- [ ] 数据库查询优化
- [ ] 添加API响应缓存
- [ ] 完善FastAPI自动文档
- [ ] 添加错误处理和日志
- [ ] 创建部署脚本

---

## 📝 开发规范和最佳实践

### 前端开发规范
**基于现有项目规范**:
- ✅ 使用现有TypeScript配置 (tsconfig.json)
- ✅ 遵循现有Vue3 Composition API模式
- ✅ 使用现有Prettier配置 (package.json中已配置)
- ✅ 保持现有目录结构和命名规范

**新增规范**:
- 组件命名使用PascalCase
- 页面文件以View.vue结尾
- Store文件使用camelCase
- API文件按模块分组

### 后端开发规范
**基于现有项目规范**:
- ✅ 遵循现有FastAPI项目结构
- ✅ 使用现有SQLAlchemy配置模式
- ✅ 保持现有日志和错误处理方式

**新增规范**:
- API端点使用RESTful设计
- 所有API返回统一格式
- 使用Pydantic进行数据验证
- 遵循Python PEP 8编码规范

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

---

## 🎯 验收标准

### 功能完整性验收
- [ ] 登录系统：支持用户名密码登录，JWT认证
- [ ] 权限控制：超级管理员和代理商角色隔离
- [ ] 仪表盘：KPI展示，图表数据可视化
- [ ] 代理商管理：完整的CRUD操作
- [ ] 优惠券系统：批次管理、分配、使用统计
- [ ] 用户管理：C端用户查看和管理
- [ ] 系统工具：优惠券模拟器功能

### 技术质量验收
- [ ] 前端：TypeScript无错误，组件复用性好
- [ ] 后端：API文档完整，数据验证严格
- [ ] 数据库：表结构合理，查询性能良好
- [ ] 安全性：权限控制严格，数据隔离有效

### 用户体验验收
- [ ] 界面美观：与现有项目风格一致
- [ ] 响应速度：页面加载时间<2秒
- [ ] 移动适配：支持移动端访问
- [ ] 操作流畅：无明显卡顿和错误

---

## 📅 优化后的时间规划

### 前端开发: 10-12天 (基于现有模板优化)
- 环境准备和基础架构: 1-2天
- 认证系统开发: 2-3天
- 核心功能模块开发: 6-8天
- 系统工具和优化: 1-2天

### 后端开发: 8-10天 (基于现有模板优化)
- 基础架构扩展: 1-2天
- 数据库模型扩展: 1-2天
- 认证系统开发: 2天
- 核心业务API开发: 3-4天
- 系统集成和优化: 1天

### 总计: 18-22天 (相比原计划节省30%时间)

**时间节省原因**:
1. 现有模板已包含大部分基础依赖
2. 数据库配置和基础架构已完成
3. UI组件库和样式系统已集成
4. 开发环境和构建配置已优化

---

## 🔧 详细技术实施方案

### 现有模板集成分析

#### Element Plus集成检查
**当前状态分析**:
```typescript
// 检查 vue3-talking-pet-admin/src/main.ts
// 当前只有基础Vue应用配置，需要添加Element Plus
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './index.css'

// 需要添加Element Plus配置
```

**需要的main.ts改造**:
```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './index.css'

// 新增：Element Plus集成
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
const pinia = createPinia()

// 注册Element Plus (利用已安装的依赖)
app.use(ElementPlus)

// 注册所有图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.mount('#app')
```

#### 需要补充的依赖
```bash
# 检查package.json后，需要补充的依赖
npm install jsonwebtoken @types/jsonwebtoken
npm install @vuelidate/core @vuelidate/validators
npm install lodash-es @types/lodash-es

# 已有的依赖可以直接使用
# ✅ element-plus@2.10.4
# ✅ @element-plus/icons-vue@2.3.1
# ✅ axios@1.10.0
# ✅ js-cookie@3.0.5
# ✅ dayjs@1.11.13
# ✅ echarts@5.6.0
# ✅ vue-echarts@7.0.3
```

### API架构设计 (基于现有Axios配置)

**现有API目录分析**:
```
vue3-talking-pet-admin/src/api/
└── admin/  # 现有目录，可直接使用
```

**API基础配置实现**:
```typescript
// src/api/index.ts (新建)
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 环境变量配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// 创建axios实例
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 从localStorage获取token (避免循环依赖)
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      router.push('/login')
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.response?.data?.message || '请求失败')
    }
    return Promise.reject(error)
  }
)

export default apiClient
```

### 认证Store设计 (基于现有Pinia配置)

**现有Store分析**:
```typescript
// 现有 src/store/user.ts 可以参考其结构
// 现有 src/store/counter.ts 展示了Pinia的使用模式
```

**认证Store实现**:
```typescript
// src/store/auth.ts (新建)
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import type { AdminUser, LoginRequest, LoginResponse } from '@/types/admin'
import apiClient from '@/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态管理
  const token = ref<string | null>(Cookies.get('admin_token') || null)
  const user = ref<AdminUser | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const isSuper = computed(() => userRole.value === 'SuperAdmin')
  const isAgent = computed(() => userRole.value === 'Agent')

  // 登录方法
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response: LoginResponse = await apiClient.post('/admin/auth/login', credentials)

      token.value = response.data.token
      user.value = response.data.user

      // 存储token到Cookie和localStorage
      Cookies.set('admin_token', response.data.token, {
        expires: 7, // 7天过期
        secure: false, // 开发环境设为false
        sameSite: 'lax'
      })
      localStorage.setItem('admin_token', response.data.token)

      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = null
    user.value = null
    Cookies.remove('admin_token')
    localStorage.removeItem('admin_token')
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) return false

    try {
      const response = await apiClient.get('/admin/auth/me')
      user.value = response.data
      return true
    } catch (error) {
      logout()
      return false
    }
  }

  return {
    token,
    user,
    loading,
    isLoggedIn,
    userRole,
    isSuper,
    isAgent,
    login,
    logout,
    checkAuth
  }
})
```

### 登录页面实现 (基于现有项目风格)

**设计理念**: 与现有HomeView.vue保持一致的设计风格

**登录页面实现**:
```vue
<!-- src/views/auth/LoginView.vue -->
<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 标题区域 - 复用现有项目风格 -->
      <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
          AI玩偶后台管理系统
        </h1>
        <p class="text-lg text-gray-600">
          请使用您的管理员账号登录
        </p>
      </div>

      <!-- 登录表单 - 使用Element Plus + Tailwind -->
      <div class="bg-white rounded-lg shadow-md p-8">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              class="h-12"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              class="h-12"
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <div class="flex items-center justify-between w-full">
              <el-checkbox v-model="loginForm.remember">
                记住我
              </el-checkbox>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="authStore.loading"
              @click="handleLogin"
              class="w-full h-12 text-lg"
            >
              {{ authStore.loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 底部信息 -->
      <div class="text-center text-sm text-gray-500">
        <p>© 2024 AI玩偶后台管理系统. 保留所有权利.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'

// 类型定义
interface LoginForm {
  username: string
  password: string
  remember: boolean
}

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 表单数据
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules: FormRules<LoginForm> = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()

    await authStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    ElMessage.success('登录成功')

    // 根据角色跳转到不同页面
    if (authStore.isSuper) {
      router.push('/dashboard')
    } else if (authStore.isAgent) {
      router.push('/dashboard') // 代理商也跳转到仪表盘，但显示不同内容
    } else {
      router.push('/')
    }

  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查用户名和密码')
  }
}
</script>
```

**2.2 路由守卫实现**
```typescript
// src/router/guards.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { ElMessage } from 'element-plus'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()

    // 如果是登录页面，且已登录，则重定向到首页
    if (to.path === '/login' && authStore.isLoggedIn) {
      if (authStore.isSuper) {
        next('/dashboard')
      } else if (authStore.isAgent) {
        next('/agent/dashboard')
      } else {
        next('/')
      }
      return
    }

    // 如果访问需要认证的页面
    if (to.meta.requiresAuth) {
      if (!authStore.token) {
        ElMessage.warning('请先登录')
        next('/login')
        return
      }

      // 验证token有效性
      const isValid = await authStore.checkAuth()
      if (!isValid) {
        ElMessage.error('登录已过期，请重新登录')
        next('/login')
        return
      }

      // 检查角色权限
      if (to.meta.roles && !to.meta.roles.includes(authStore.userRole)) {
        ElMessage.error('您没有权限访问此页面')
        next('/403')
        return
      }
    }

    next()
  })
}
```

### 后端技术实施细节 (基于现有FastAPI模板)

#### 配置文件扩展
**扩展现有 app/core/config.py**:
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置文件 - 扩展版本
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置"""

    # 基础配置 (保持现有)
    app_name: str = "AI玩偶后台管理系统"
    app_version: str = "1.0.0"
    debug: bool = True

    # 服务器配置 (保持现有)
    host: str = "0.0.0.0"
    port: int = 8000

    # 数据库配置 (保持现有)
    database_url: str = "mysql+pymysql://username:password@localhost:3306/fastapi_talking_pet"
    database_echo: bool = False

    # CORS配置 (保持现有)
    cors_origins: list = ["http://localhost:5173", "http://localhost:3000"]
    cors_methods: list = ["*"]
    cors_headers: list = ["*"]

    # 新增：JWT认证配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 60 * 24 * 7  # 7天

    # 新增：密码加密配置
    pwd_context_schemes: list = ["bcrypt"]
    pwd_context_deprecated: str = "auto"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局设置实例
settings = Settings()
```

#### 数据库模型设计 (基于现有SQLAlchemy配置)
**新增 app/models/admin_user.py**:
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员用户数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Role(Base):
    """角色模型"""
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(50), unique=True, nullable=False, comment='角色英文标识')
    description = Column(String(255), comment='角色描述')

    # 关系
    users = relationship("AdminUser", back_populates="role")

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"


class AdminUser(Base):
    """管理员用户模型"""
    __tablename__ = "admin_users"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment='登录账号')
    password_hash = Column(String(255), nullable=False, comment='bcrypt加密后的密码')
    nickname = Column(String(50), comment='用户昵称')
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False, comment='外键->roles.id')
    parent_id = Column(Integer, ForeignKey("admin_users.id"), nullable=True, comment='创建者ID')
    status = Column(Boolean, default=True, comment='状态: 1=启用, 0=禁用')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    role = relationship("Role", back_populates="users")
    parent = relationship("AdminUser", remote_side=[id])
    children = relationship("AdminUser")

    def __repr__(self):
        return f"<AdminUser(id={self.id}, username='{self.username}', role='{self.role.name if self.role else None}')>"
```

#### JWT认证实现
```python
# app/core/auth.py
from datetime import datetime, timedelta
from typing import Optional
import jwt
from passlib.context import CryptContext
from app.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except jwt.PyJWTError:
        return None
```

#### API路由实现
```python
# app/api/v1/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas.auth import LoginRequest, LoginResponse, UserResponse
from app.models.admin_user import AdminUser
from app.core.auth import verify_password, create_access_token, verify_token

router = APIRouter()
security = HTTPBearer()

@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    # 验证用户
    user = db.query(AdminUser).filter(
        AdminUser.username == login_data.username,
        AdminUser.status == True
    ).first()

    if not user or not verify_password(login_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )

    # 生成token
    access_token = create_access_token(
        data={"sub": str(user.id), "role": user.role.name}
    )

    return LoginResponse(
        code=0,
        message="登录成功",
        data={
            "token": access_token,
            "user": UserResponse.from_orm(user),
            "expires_in": 3600 * 24 * 7  # 7天
        }
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    payload = verify_token(credentials.credentials)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token无效"
        )

    user_id = payload.get("sub")
    user = db.query(AdminUser).filter(AdminUser.id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )

    return UserResponse.from_orm(user)
```

---

## �🚨 风险控制

### 技术风险
- 定期代码审查
- 及时技术调研
- 保持技术栈稳定

### 进度风险
- 每日进度跟踪
- 及时调整计划
- 关键节点检查

### 质量风险
- 严格测试流程
- 代码质量检查
- 用户体验验证

---

## 📋 详细开发任务清单

### 前端开发任务清单

#### 第一阶段：基础架构 (2-3天)

**Day 1: 环境配置**
- [ ] 安装Element Plus和相关依赖
- [ ] 配置main.ts，集成Element Plus
- [ ] 设置环境变量文件(.env.development, .env.production)
- [ ] 配置Tailwind CSS与Element Plus兼容
- [ ] 创建基础目录结构

**Day 2: API架构**
- [ ] 创建API基础配置 (src/api/index.ts)
- [ ] 实现Axios请求拦截器
- [ ] 创建API响应类型定义 (src/types/api.ts)
- [ ] 实现错误处理机制
- [ ] 创建认证相关API接口 (src/api/auth.ts)

**Day 3: 状态管理**
- [ ] 创建认证Store (src/store/auth.ts)
- [ ] 实现token管理和持久化
- [ ] 创建权限Store (src/store/permissions.ts)
- [ ] 测试状态管理功能

#### 第二阶段：登录系统 (3-4天)

**Day 4: 登录页面开发**
- [ ] 创建LoginView.vue页面
- [ ] 实现登录表单UI
- [ ] 集成Element Plus表单组件
- [ ] 添加表单验证规则
- [ ] 实现响应式设计

**Day 5: 认证逻辑**
- [ ] 集成认证Store到登录页面
- [ ] 实现登录提交逻辑
- [ ] 添加加载状态和错误处理
- [ ] 实现"记住我"功能
- [ ] 测试登录流程

**Day 6: 路由控制**
- [ ] 创建路由守卫 (src/router/guards.ts)
- [ ] 实现权限检查逻辑
- [ ] 配置登录路由
- [ ] 创建403/404错误页面
- [ ] 测试路由权限控制

**Day 7: 布局改造**
- [ ] 改造TheLayout.vue为管理后台布局
- [ ] 改造TheNavbar.vue为后台导航
- [ ] 创建侧边栏菜单组件
- [ ] 实现用户信息显示
- [ ] 实现退出登录功能

#### 第三阶段：核心功能模块 (8-10天)

**Day 8-9: 仪表盘开发**
- [ ] 创建Dashboard页面结构
- [ ] 实现KPI卡片组件
- [ ] 集成ECharts图表库
- [ ] 实现销售趋势图
- [ ] 实现代理商业绩饼图
- [ ] 添加数据刷新功能
- [ ] 实现角色差异化显示

**Day 10-11: 代理商管理**
- [ ] 创建代理商列表页面
- [ ] 实现搜索和分页功能
- [ ] 创建代理商创建表单
- [ ] 实现代理商编辑功能
- [ ] 添加状态切换功能
- [ ] 实现批量操作

**Day 12-14: 优惠券管理**
- [ ] 创建优惠券批次管理页面
- [ ] 实现批次创建和编辑
- [ ] 创建优惠券分配功能
- [ ] 实现优惠券实例列表
- [ ] 添加高级筛选功能
- [ ] 实现批量操作
- [ ] 创建使用统计页面

**Day 15: 用户管理**
- [ ] 创建用户列表页面
- [ ] 实现用户搜索功能
- [ ] 添加用户详情查看
- [ ] 实现数据导出功能

**Day 16-17: 系统工具**
- [ ] 创建优惠券模拟器页面
- [ ] 实现参数选择功能
- [ ] 添加实时日志显示
- [ ] 实现批量模拟功能
- [ ] 系统优化和性能调优

### 后端开发任务清单

#### 第一阶段：项目初始化 (2-3天)

**Day 1: 项目搭建**
- [ ] 创建FastAPI项目结构
- [ ] 配置虚拟环境和依赖
- [ ] 设置项目配置文件 (config.py)
- [ ] 配置日志系统
- [ ] 创建基础目录结构

**Day 2: 数据库设计**
- [ ] 设计完整的数据库表结构
- [ ] 创建SQLAlchemy模型
- [ ] 配置数据库连接
- [ ] 设置Alembic数据库迁移
- [ ] 创建初始化数据脚本

**Day 3: 基础架构**
- [ ] 创建Pydantic模式定义
- [ ] 实现统一响应格式
- [ ] 配置CORS中间件
- [ ] 创建异常处理器
- [ ] 设置API文档

#### 第二阶段：认证系统 (2-3天)

**Day 4: JWT认证**
- [ ] 实现JWT token生成和验证
- [ ] 创建密码加密工具
- [ ] 实现认证中间件
- [ ] 创建权限装饰器
- [ ] 测试认证功能

**Day 5: 登录API**
- [ ] 实现登录接口
- [ ] 创建用户信息接口
- [ ] 实现token刷新机制
- [ ] 添加登录日志记录
- [ ] 测试登录流程

**Day 6: 权限控制**
- [ ] 实现RBAC权限系统
- [ ] 创建数据隔离中间件
- [ ] 实现API权限检查
- [ ] 添加权限缓存
- [ ] 测试权限控制

#### 第三阶段：业务API开发 (6-8天)

**Day 7: 用户管理API**
- [ ] 实现管理员用户CRUD
- [ ] 创建代理商管理接口
- [ ] 实现用户状态管理
- [ ] 添加搜索和分页
- [ ] 测试用户管理功能

**Day 8-10: 优惠券系统API**
- [ ] 实现优惠券档次管理
- [ ] 创建优惠券批次API
- [ ] 实现优惠券分配逻辑
- [ ] 创建优惠券使用接口
- [ ] 实现库存管理
- [ ] 添加统计接口
- [ ] 测试优惠券系统

**Day 11: 数据统计API**
- [ ] 实现仪表盘数据接口
- [ ] 创建销售统计API
- [ ] 实现用户统计接口
- [ ] 添加实时数据更新
- [ ] 测试统计功能

**Day 12: 系统工具API**
- [ ] 实现优惠券模拟器API
- [ ] 创建数据导出接口
- [ ] 实现系统监控API
- [ ] 测试工具功能

#### 第四阶段：集成优化 (2-3天)

**Day 13-14: 前后端集成**
- [ ] API接口联调测试
- [ ] 解决跨域问题
- [ ] 优化API响应时间
- [ ] 统一错误处理
- [ ] 完善API文档

**Day 15: 性能优化**
- [ ] 数据库查询优化
- [ ] 添加API限流
- [ ] 实现数据验证
- [ ] 加强安全防护
- [ ] 添加监控日志

---

## 🔧 技术实现细节补充

### 类型定义文件

```typescript
// src/types/auth.ts
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  code: number
  message: string
  data: {
    token: string
    user: AdminUser
    expires_in: number
  }
}

export interface AdminUser {
  id: number
  username: string
  nickname: string
  role_id: number
  role: string
  status: boolean
  created_at: string
  updated_at: string
}

// src/types/api.ts
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  size: number
}

export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}
```

### 环境配置文件

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=AI玩偶后台管理系统
VITE_APP_VERSION=1.0.0

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=AI玩偶后台管理系统
VITE_APP_VERSION=1.0.0
```

### 后端配置文件

```python
# app/config.py
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "AI玩偶后台管理系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = False

    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://user:password@localhost/ai_doll_admin"

    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天

    # CORS配置
    ALLOWED_ORIGINS: list = ["http://localhost:5173", "http://localhost:3000"]

    class Config:
        env_file = ".env"

settings = Settings()
```

### 数据库初始化脚本

```python
# scripts/init_db.py
from app.database import engine, SessionLocal
from app.models import Base, Role, AdminUser
from app.core.auth import get_password_hash

def init_database():
    # 创建所有表
    Base.metadata.create_all(bind=engine)

    db = SessionLocal()
    try:
        # 创建角色
        if not db.query(Role).filter(Role.name == "SuperAdmin").first():
            super_admin_role = Role(name="SuperAdmin", description="超级管理员")
            db.add(super_admin_role)
            db.commit()

        if not db.query(Role).filter(Role.name == "Agent").first():
            agent_role = Role(name="Agent", description="代理商")
            db.add(agent_role)
            db.commit()

        # 创建默认超级管理员
        if not db.query(AdminUser).filter(AdminUser.username == "admin").first():
            super_admin = AdminUser(
                username="admin",
                password_hash=get_password_hash("admin123"),
                nickname="系统管理员",
                role_id=1,  # SuperAdmin
                status=True
            )
            db.add(super_admin)
            db.commit()

        print("数据库初始化完成")

    except Exception as e:
        print(f"数据库初始化失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
```

### 前端路由配置

```typescript
// src/router/index.ts
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/DashboardView.vue'),
    meta: {
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      title: '仪表盘'
    }
  },
  {
    path: '/agents',
    name: 'Agents',
    component: () => import('@/views/agents/AgentListView.vue'),
    meta: {
      requiresAuth: true,
      roles: ['SuperAdmin'],
      title: '代理商管理'
    }
  },
  {
    path: '/coupons',
    children: [
      {
        path: 'batches',
        name: 'CouponBatches',
        component: () => import('@/views/coupons/BatchListView.vue'),
        meta: {
          requiresAuth: true,
          roles: ['SuperAdmin'],
          title: '优惠券批次'
        }
      },
      {
        path: 'instances',
        name: 'CouponInstances',
        component: () => import('@/views/coupons/InstanceListView.vue'),
        meta: {
          requiresAuth: true,
          roles: ['SuperAdmin', 'Agent'],
          title: '优惠券实例'
        }
      }
    ]
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/users/UserListView.vue'),
    meta: {
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      title: '用户管理'
    }
  },
  {
    path: '/tools',
    children: [
      {
        path: 'coupon-simulator',
        name: 'CouponSimulator',
        component: () => import('@/views/tools/CouponSimulatorView.vue'),
        meta: {
          requiresAuth: true,
          roles: ['SuperAdmin'],
          title: '优惠券模拟器'
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/profile/ProfileView.vue'),
    meta: {
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      title: '个人中心'
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403View.vue')
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404View.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router
```

---

## 🧪 测试策略

### 前端测试

#### 单元测试
```typescript
// tests/unit/store/auth.spec.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/store/auth'

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with correct default state', () => {
    const authStore = useAuthStore()
    expect(authStore.token).toBe(null)
    expect(authStore.user).toBe(null)
    expect(authStore.isLoggedIn).toBe(false)
  })

  it('should handle login correctly', async () => {
    const authStore = useAuthStore()
    // Mock API response
    const mockResponse = {
      code: 0,
      message: '登录成功',
      data: {
        token: 'mock-token',
        user: { id: 1, username: 'admin', role: 'SuperAdmin' },
        expires_in: 3600
      }
    }

    // Test login logic
    // ...
  })
})
```

#### 组件测试
```typescript
// tests/unit/components/LoginView.spec.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import LoginView from '@/views/auth/LoginView.vue'

describe('LoginView', () => {
  it('should render login form correctly', () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [createPinia()]
      }
    })

    expect(wrapper.find('input[placeholder="请输入用户名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="请输入密码"]').exists()).toBe(true)
    expect(wrapper.find('button').text()).toContain('登录')
  })

  it('should validate form inputs', async () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [createPinia()]
      }
    })

    // Test form validation
    // ...
  })
})
```

### 后端测试

#### API测试
```python
# tests/test_auth.py
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.database import get_db
from tests.conftest import override_get_db

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)

def test_login_success():
    response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "admin", "password": "admin123"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert "token" in data["data"]
    assert data["data"]["user"]["username"] == "admin"

def test_login_invalid_credentials():
    response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "admin", "password": "wrong_password"}
    )
    assert response.status_code == 401
    data = response.json()
    assert "用户名或密码错误" in data["detail"]

def test_protected_route_without_token():
    response = client.get("/api/v1/admin/auth/me")
    assert response.status_code == 403

def test_protected_route_with_valid_token():
    # First login to get token
    login_response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "admin", "password": "admin123"}
    )
    token = login_response.json()["data"]["token"]

    # Use token to access protected route
    response = client.get(
        "/api/v1/admin/auth/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "admin"
```

---

## 🚀 部署方案

### 前端部署

#### 构建配置
```bash
# 生产环境构建
npm run build

# 构建产物
dist/
├── index.html
├── assets/
│   ├── index-[hash].js
│   ├── index-[hash].css
│   └── ...
└── favicon.ico
```

#### Nginx配置
```nginx
# /etc/nginx/sites-available/ai-doll-admin
server {
    listen 80;
    server_name admin.example.com;

    root /var/www/ai-doll-admin/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 后端部署

#### Docker配置
```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:password@db:3306/ai_doll_admin
      - SECRET_KEY=your-production-secret-key
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=ai_doll_admin
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./dist:/var/www/html
    depends_on:
      - backend

volumes:
  mysql_data:
```

#### 生产环境启动脚本
```bash
#!/bin/bash
# deploy.sh

# 拉取最新代码
git pull origin main

# 前端构建
cd frontend
npm install
npm run build
cd ..

# 后端部署
docker-compose down
docker-compose build
docker-compose up -d

# 数据库迁移
docker-compose exec backend alembic upgrade head

# 检查服务状态
docker-compose ps

echo "部署完成！"
```

---

## 📊 项目里程碑

### 第一里程碑：登录系统完成 (第7天)
**验收标准**:
- [ ] 登录页面功能完整
- [ ] JWT认证正常工作
- [ ] 路由权限控制有效
- [ ] 前后端完全对接

### 第二里程碑：核心功能完成 (第20天)
**验收标准**:
- [ ] 仪表盘数据展示正确
- [ ] 代理商管理功能完整
- [ ] 优惠券系统正常运行
- [ ] 用户管理功能可用

### 第三里程碑：系统完整交付 (第30天)
**验收标准**:
- [ ] 所有功能模块完成
- [ ] 系统性能达标
- [ ] 安全测试通过
- [ ] 部署文档完整

---

## 📚 开发文档

### API文档
- 使用FastAPI自动生成的Swagger文档
- 访问地址: http://localhost:8000/docs
- 包含所有接口的详细说明和测试功能

### 前端组件文档
- 使用Storybook展示组件库
- 包含所有自定义组件的使用示例
- 提供交互式的组件测试环境

### 部署文档
- 详细的环境配置说明
- 数据库初始化步骤
- 服务器部署指南
- 常见问题解决方案

---

## 🎯 成功标准

### 功能完整性
- ✅ 所有PRD要求的功能100%实现
- ✅ 用户体验流畅，无明显卡顿
- ✅ 响应式设计，支持移动端访问

### 技术质量
- ✅ 代码质量高，结构清晰
- ✅ 类型安全，无TypeScript错误
- ✅ 测试覆盖率达到80%以上

### 安全性
- ✅ 权限控制严格，无数据泄露风险
- ✅ 输入验证完整，防止注入攻击
- ✅ 认证机制安全可靠

### 性能表现
- ✅ 页面加载时间小于2秒
- ✅ API响应时间小于500ms
- ✅ 支持并发用户数100+

### 可维护性
- ✅ 代码结构清晰，易于扩展
- ✅ 文档完整，便于后续维护
- ✅ 部署流程自动化

---

**本开发方案为AI玩偶后台管理系统提供了完整的技术路线图，确保项目能够按时、按质量要求交付。所有技术选型都基于现有项目模板，保证了开发的连续性和一致性。**
