<template>
  <div class="empty-state">
    <div class="text-center py-12">
      <!-- 图标 -->
      <div class="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <el-icon :size="32" color="#9CA3AF">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <!-- 标题 -->
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        {{ title }}
      </h3>
      
      <!-- 描述 -->
      <p class="text-gray-500 mb-6 max-w-sm mx-auto">
        {{ description }}
      </p>
      
      <!-- 操作按钮 -->
      <div v-if="showAction" class="space-y-2">
        <el-button 
          v-if="actionText"
          :type="actionType" 
          :icon="actionIcon"
          @click="handleAction"
        >
          {{ actionText }}
        </el-button>
        
        <div v-if="secondaryActionText">
          <el-button 
            type="default"
            link
            @click="handleSecondaryAction"
          >
            {{ secondaryActionText }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  DocumentRemove,
  Search,
  FolderOpened,
  Warning,
  Plus,
  Refresh
} from '@element-plus/icons-vue'

interface Props {
  type?: 'no-data' | 'no-search' | 'empty-folder' | 'error'
  title?: string
  description?: string
  actionText?: string
  actionType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  actionIcon?: any
  secondaryActionText?: string
  showAction?: boolean
}

interface Emits {
  (e: 'action'): void
  (e: 'secondary-action'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'no-data',
  showAction: true,
  actionType: 'primary'
})

const emit = defineEmits<Emits>()

// 默认配置
const defaultConfig = {
  'no-data': {
    title: '暂无数据',
    description: '当前没有任何数据，您可以创建一个新的项目开始使用。',
    icon: DocumentRemove,
    actionText: '创建新项目',
    actionIcon: Plus
  },
  'no-search': {
    title: '未找到匹配结果',
    description: '请尝试调整搜索条件或清空筛选器重新搜索。',
    icon: Search,
    actionText: '清空筛选',
    actionIcon: Refresh
  },
  'empty-folder': {
    title: '文件夹为空',
    description: '这个文件夹还没有任何内容，您可以上传文件或创建新的文件夹。',
    icon: FolderOpened,
    actionText: '上传文件',
    actionIcon: Plus
  },
  'error': {
    title: '加载失败',
    description: '数据加载时出现错误，请检查网络连接或稍后重试。',
    icon: Warning,
    actionText: '重新加载',
    actionIcon: Refresh
  }
}

// 计算属性
const config = computed(() => defaultConfig[props.type])

const title = computed(() => props.title || config.value.title)

const description = computed(() => props.description || config.value.description)

const iconComponent = computed(() => config.value.icon)

const actionText = computed(() => props.actionText || config.value.actionText)

const actionIcon = computed(() => props.actionIcon || config.value.actionIcon)

// 事件处理
function handleAction() {
  emit('action')
}

function handleSecondaryAction() {
  emit('secondary-action')
}
</script>

<style scoped>
.empty-state {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
