/**
 * 认证相关类型定义
 */

// 用户角色
export type UserRole = 'SuperAdmin' | 'Agent'

// 登录请求
export interface LoginRequest {
  username: string
  password: string
}

// 管理员用户信息
export interface AdminUser {
  id: number
  username: string
  nickname: string
  role_id: number
  role: UserRole
  parent_id?: number
  status: boolean
  created_at: string
  updated_at: string
}

// 登录响应
export interface LoginResponse {
  code: number
  message: string
  data: {
    token: string
    user: AdminUser
    expires_in: number
  }
}

// 用户信息响应
export interface UserInfoResponse {
  code: number
  message: string
  data: AdminUser
}

// 修改密码请求
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
  confirm_password: string
}

// JWT Token 载荷
export interface TokenPayload {
  sub: string // 用户ID
  role: UserRole
  exp: number
  iat: number
}
