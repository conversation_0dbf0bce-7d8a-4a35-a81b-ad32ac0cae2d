/**
 * 管理后台相关 API
 */

import { get, post, put, del } from './index'
import type { 
  Agent, 
  CreateAgentRequest, 
  UpdateAgentRequest,
  CouponBatch,
  Coupon,
  CouponTier,
  CreateCouponBatchRequest,
  AssignCouponRequest,
  EndUser,
  DashboardStats,
  CouponSimulateRequest
} from '@/types/admin'
import type { ApiResponse, PaginationResponse, QueryParams } from '@/types/api'

// ==================== 代理商管理 ====================

/**
 * 获取代理商列表
 */
export function getAgents(params: QueryParams): Promise<ApiResponse<PaginationResponse<Agent>>> {
  return get('/admin/agents', params)
}

/**
 * 创建代理商
 */
export function createAgent(data: CreateAgentRequest): Promise<ApiResponse<Agent>> {
  return post('/admin/agents', data)
}

/**
 * 更新代理商
 */
export function updateAgent(id: number, data: UpdateAgentRequest): Promise<ApiResponse<Agent>> {
  return put(`/admin/agents/${id}`, data)
}

/**
 * 删除代理商
 */
export function deleteAgent(id: number): Promise<ApiResponse> {
  return del(`/admin/agents/${id}`)
}

/**
 * 获取代理商详情
 */
export function getAgent(id: number): Promise<ApiResponse<Agent>> {
  return get(`/admin/agents/${id}`)
}

// ==================== 优惠券管理 ====================

/**
 * 获取优惠券档次列表
 */
export function getCouponTiers(): Promise<ApiResponse<CouponTier[]>> {
  return get('/admin/coupon-tiers')
}

/**
 * 获取优惠券批次列表
 */
export function getCouponBatches(params: QueryParams): Promise<ApiResponse<PaginationResponse<CouponBatch>>> {
  return get('/admin/coupon-batches', params)
}

/**
 * 创建优惠券批次
 */
export function createCouponBatch(data: CreateCouponBatchRequest): Promise<ApiResponse<CouponBatch>> {
  return post('/admin/coupon-batches', data)
}

/**
 * 获取优惠券实例列表
 */
export function getCoupons(params: QueryParams): Promise<ApiResponse<PaginationResponse<Coupon>>> {
  return get('/admin/coupons', params)
}

/**
 * 分配优惠券
 */
export function assignCoupons(data: AssignCouponRequest): Promise<ApiResponse> {
  return post('/admin/coupons/assign', data)
}

/**
 * 获取优惠券统计
 */
export function getCouponStats(batchId?: number): Promise<ApiResponse<any>> {
  const params = batchId ? { batch_id: batchId } : {}
  return get('/admin/coupons/stats', params)
}

// ==================== 用户管理 ====================

/**
 * 获取C端用户列表
 */
export function getEndUsers(params: QueryParams): Promise<ApiResponse<PaginationResponse<EndUser>>> {
  return get('/admin/users', params)
}

/**
 * 获取用户详情
 */
export function getEndUser(id: number): Promise<ApiResponse<EndUser>> {
  return get(`/admin/users/${id}`)
}

// ==================== 仪表盘 ====================

/**
 * 获取仪表盘统计数据
 */
export function getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
  return get('/admin/dashboard/stats')
}

// ==================== 系统工具 ====================

/**
 * 优惠券使用模拟
 */
export function simulateCouponUsage(data: CouponSimulateRequest): Promise<ApiResponse<any>> {
  return post('/admin/tools/coupon-simulator', data)
}

/**
 * 导出数据
 */
export function exportData(type: 'agents' | 'users' | 'coupons', params?: any): Promise<Blob> {
  return get(`/admin/export/${type}`, params)
}
