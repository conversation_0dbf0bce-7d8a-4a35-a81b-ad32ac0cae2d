/**
 * 表格操作 Composable
 */

import { ref, reactive } from 'vue'
import type { QueryParams, PaginationResponse } from '@/types/api'

interface TableState<T> {
  loading: boolean
  data: T[]
  total: number
  page: number
  size: number
}

interface SearchForm {
  keyword?: string
  [key: string]: any
}

export function useTable<T = any>(
  fetchFunction: (params: QueryParams) => Promise<{ code: number; data: PaginationResponse<T> }>,
  initialPageSize = 20
) {
  // 表格状态
  const tableState = reactive<TableState<T>>({
    loading: false,
    data: [],
    total: 0,
    page: 1,
    size: initialPageSize
  })

  // 搜索表单
  const searchForm = reactive<SearchForm>({
    keyword: ''
  })

  // 排序参数
  const sortParams = ref<{ prop?: string; order?: string }>({})

  /**
   * 获取数据
   */
  async function fetchData() {
    try {
      tableState.loading = true
      
      const params: QueryParams = {
        page: tableState.page,
        size: tableState.size,
        keyword: searchForm.keyword || undefined,
        filters: getFilters(),
        sort: getSortParams()
      }

      const response = await fetchFunction(params)
      
      if (response.code === 0) {
        tableState.data = response.data.items
        tableState.total = response.data.total
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      tableState.data = []
      tableState.total = 0
    } finally {
      tableState.loading = false
    }
  }

  /**
   * 获取过滤参数
   */
  function getFilters() {
    const filters: Record<string, any> = {}
    
    Object.keys(searchForm).forEach(key => {
      if (key !== 'keyword' && searchForm[key] !== undefined && searchForm[key] !== '') {
        filters[key] = searchForm[key]
      }
    })
    
    return Object.keys(filters).length > 0 ? filters : undefined
  }

  /**
   * 获取排序参数
   */
  function getSortParams() {
    if (!sortParams.value.prop || !sortParams.value.order) {
      return undefined
    }
    
    return {
      field: sortParams.value.prop,
      order: sortParams.value.order === 'ascending' ? 'asc' : 'desc'
    }
  }

  /**
   * 搜索
   */
  function handleSearch() {
    tableState.page = 1
    fetchData()
  }

  /**
   * 重置搜索
   */
  function handleReset() {
    // 重置搜索表单
    Object.keys(searchForm).forEach(key => {
      if (key === 'keyword') {
        searchForm[key] = ''
      } else {
        searchForm[key] = undefined
      }
    })
    
    // 重置排序
    sortParams.value = {}
    
    // 重置分页
    tableState.page = 1
    
    // 重新获取数据
    fetchData()
  }

  /**
   * 分页大小变化
   */
  function handleSizeChange(size: number) {
    tableState.size = size
    tableState.page = 1
    fetchData()
  }

  /**
   * 当前页变化
   */
  function handleCurrentChange(page: number) {
    tableState.page = page
    fetchData()
  }

  /**
   * 排序变化
   */
  function handleSortChange({ prop, order }: { prop: string; order: string }) {
    sortParams.value = { prop, order }
    fetchData()
  }

  /**
   * 刷新数据
   */
  function refresh() {
    fetchData()
  }

  /**
   * 重新加载数据（重置到第一页）
   */
  function reload() {
    tableState.page = 1
    fetchData()
  }

  /**
   * 设置搜索表单字段
   */
  function setSearchField(key: string, value: any) {
    searchForm[key] = value
  }

  /**
   * 获取搜索表单字段
   */
  function getSearchField(key: string) {
    return searchForm[key]
  }

  return {
    // 状态
    tableState,
    searchForm,
    sortParams,

    // 方法
    fetchData,
    handleSearch,
    handleReset,
    handleSizeChange,
    handleCurrentChange,
    handleSortChange,
    refresh,
    reload,
    setSearchField,
    getSearchField
  }
}
