/**
 * 权限检查 Composable
 */

import { computed } from 'vue'
import { useAuthStore } from '@/store/auth'
import type { UserRole } from '@/types/auth'

export function usePermission() {
  const authStore = useAuthStore()

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: UserRole): boolean => {
    return authStore.userRole === role
  }

  /**
   * 检查是否有任意一个角色
   */
  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.includes(authStore.userRole as UserRole)
  }

  /**
   * 检查是否为超级管理员
   */
  const isSuper = computed(() => authStore.isSuper)

  /**
   * 检查是否为代理商
   */
  const isAgent = computed(() => authStore.isAgent)

  /**
   * 检查是否已登录
   */
  const isLoggedIn = computed(() => authStore.isLoggedIn)

  /**
   * 获取当前用户角色
   */
  const currentRole = computed(() => authStore.userRole)

  /**
   * 获取当前用户信息
   */
  const currentUser = computed(() => authStore.user)

  /**
   * 检查菜单项是否可见
   */
  const canAccessMenu = (requiredRoles?: UserRole[]): boolean => {
    if (!isLoggedIn.value) return false
    if (!requiredRoles || requiredRoles.length === 0) return true
    return hasAnyRole(requiredRoles)
  }

  /**
   * 检查是否可以访问代理商管理
   */
  const canManageAgents = computed(() => isSuper.value)

  /**
   * 检查是否可以创建优惠券批次
   */
  const canCreateCouponBatch = computed(() => isSuper.value)

  /**
   * 检查是否可以分配优惠券
   */
  const canAssignCoupons = computed(() => isSuper.value)

  /**
   * 检查是否可以使用系统工具
   */
  const canUseSystemTools = computed(() => isSuper.value)

  /**
   * 检查是否可以查看所有用户
   */
  const canViewAllUsers = computed(() => isSuper.value)

  /**
   * 检查是否可以查看全局统计
   */
  const canViewGlobalStats = computed(() => isSuper.value)

  return {
    // 基础权限检查
    hasRole,
    hasAnyRole,
    isSuper,
    isAgent,
    isLoggedIn,
    currentRole,
    currentUser,
    canAccessMenu,

    // 具体功能权限
    canManageAgents,
    canCreateCouponBatch,
    canAssignCoupons,
    canUseSystemTools,
    canViewAllUsers,
    canViewGlobalStats
  }
}
