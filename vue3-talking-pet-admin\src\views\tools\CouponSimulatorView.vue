<template>
  <div class="coupon-simulator">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">优惠券使用模拟器</h1>
      <p class="text-gray-600 mt-1">模拟优惠券的使用过程，用于测试和演示</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 模拟器控制面板 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">模拟参数设置</h3>
        
        <el-form
          ref="simulatorFormRef"
          :model="simulatorForm"
          :rules="simulatorRules"
          label-width="120px"
        >
          <el-form-item label="选择代理商" prop="agent_id">
            <el-select 
              v-model="simulatorForm.agent_id" 
              placeholder="选择代理商（可选）" 
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="agent in agentList"
                :key="agent.id"
                :label="agent.nickname || agent.username"
                :value="agent.id"
              />
            </el-select>
            <div class="text-xs text-gray-500 mt-1">
              不选择代理商将随机选择可用优惠券
            </div>
          </el-form-item>
          
          <el-form-item label="选择用户" prop="user_id">
            <el-select 
              v-model="simulatorForm.user_id" 
              placeholder="选择用户（可选）" 
              clearable
              style="width: 100%"
              filterable
              remote
              :remote-method="searchUsers"
              :loading="userSearchLoading"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="`${user.nickname || '未设置昵称'} (ID: ${user.id})`"
                :value="user.id"
              />
            </el-select>
            <div class="text-xs text-gray-500 mt-1">
              不选择用户将随机选择用户
            </div>
          </el-form-item>
          
          <el-form-item label="模拟数量" prop="quantity">
            <el-input-number
              v-model="simulatorForm.quantity"
              :min="1"
              :max="100"
              style="width: 100%"
            />
            <div class="text-xs text-gray-500 mt-1">
              一次最多模拟100张优惠券的使用
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              :loading="simulatorLoading"
              :disabled="!canSimulate"
              @click="handleSimulate"
              class="w-full"
            >
              {{ simulatorLoading ? '模拟中...' : '开始模拟' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 统计信息 -->
        <div class="mt-6 pt-6 border-t border-gray-200">
          <h4 class="text-md font-medium text-gray-900 mb-3">模拟统计</h4>
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{{ simulationStats.total }}</div>
              <div class="text-sm text-gray-600">总模拟次数</div>
            </div>
            <div class="text-center p-3 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{{ simulationStats.success }}</div>
              <div class="text-sm text-gray-600">成功次数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时日志 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">实时日志</h3>
          <el-button size="small" :icon="Delete" @click="clearLogs">
            清空日志
          </el-button>
        </div>
        
        <div 
          ref="logContainerRef"
          class="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm"
        >
          <div v-if="logs.length === 0" class="text-gray-500 text-center py-8">
            暂无日志，点击"开始模拟"查看实时日志
          </div>
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="mb-1"
            :class="{
              'text-green-400': log.type === 'success',
              'text-red-400': log.type === 'error',
              'text-yellow-400': log.type === 'warning',
              'text-blue-400': log.type === 'info'
            }"
          >
            <span class="text-gray-500">[{{ log.timestamp }}]</span>
            {{ log.message }}
          </div>
        </div>
      </div>
    </div>

    <!-- 模拟结果 -->
    <div v-if="simulationResult" class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">模拟结果</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{{ simulationResult.total_simulated }}</div>
          <div class="text-sm text-gray-600">模拟总数</div>
        </div>
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ simulationResult.success_count }}</div>
          <div class="text-sm text-gray-600">成功数量</div>
        </div>
        <div class="text-center p-4 bg-red-50 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{{ simulationResult.failed_count }}</div>
          <div class="text-sm text-gray-600">失败数量</div>
        </div>
      </div>

      <div v-if="simulationResult.details && simulationResult.details.length > 0">
        <h4 class="text-md font-medium text-gray-900 mb-3">详细结果</h4>
        <el-table :data="simulationResult.details" stripe style="width: 100%">
          <el-table-column prop="coupon_code" label="优惠券码" width="150" />
          <el-table-column prop="user_id" label="用户ID" width="100" />
          <el-table-column prop="order_id" label="订单ID" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                {{ row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="说明" min-width="200" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { simulateCouponUsage, getAgents, getEndUsers } from '@/api/admin'
import type { Agent, EndUser, CouponSimulateRequest } from '@/types/admin'
import dayjs from 'dayjs'

// 响应式数据
const simulatorLoading = ref(false)
const userSearchLoading = ref(false)
const agentList = ref<Agent[]>([])
const userList = ref<EndUser[]>([])
const simulatorFormRef = ref<FormInstance>()
const logContainerRef = ref<HTMLElement>()

// 模拟器表单
interface SimulatorForm {
  agent_id: number | undefined
  user_id: number | undefined
  quantity: number
}

const simulatorForm = reactive<SimulatorForm>({
  agent_id: undefined,
  user_id: undefined,
  quantity: 1
})

// 日志接口
interface LogEntry {
  timestamp: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
}

const logs = ref<LogEntry[]>([])

// 模拟统计
const simulationStats = reactive({
  total: 0,
  success: 0
})

// 模拟结果
const simulationResult = ref<any>(null)

// 表单验证规则
const simulatorRules: FormRules<SimulatorForm> = {
  quantity: [
    { required: true, message: '请输入模拟数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '数量必须在1-100之间', trigger: 'blur' }
  ]
}

// 是否可以模拟
const canSimulate = computed(() => {
  return simulatorForm.quantity > 0 && simulatorForm.quantity <= 100
})

// 添加日志
function addLog(type: LogEntry['type'], message: string) {
  logs.value.push({
    timestamp: dayjs().format('HH:mm:ss'),
    type,
    message
  })
  
  // 自动滚动到底部
  nextTick(() => {
    if (logContainerRef.value) {
      logContainerRef.value.scrollTop = logContainerRef.value.scrollHeight
    }
  })
}

// 清空日志
function clearLogs() {
  logs.value = []
}

// 获取代理商列表
async function fetchAgents() {
  try {
    const response = await getAgents({ page: 1, size: 1000 })
    if (response.code === 0) {
      agentList.value = response.data.items.filter(agent => agent.status)
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
  }
}

// 搜索用户
async function searchUsers(query: string) {
  if (!query) {
    userList.value = []
    return
  }
  
  try {
    userSearchLoading.value = true
    const response = await getEndUsers({
      page: 1,
      size: 20,
      keyword: query
    })
    if (response.code === 0) {
      userList.value = response.data.items
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userSearchLoading.value = false
  }
}

// 开始模拟
async function handleSimulate() {
  if (!simulatorFormRef.value) return
  
  try {
    await simulatorFormRef.value.validate()
    simulatorLoading.value = true
    simulationResult.value = null
    
    addLog('info', `开始模拟 ${simulatorForm.quantity} 张优惠券的使用...`)
    
    if (simulatorForm.agent_id) {
      const agent = agentList.value.find(a => a.id === simulatorForm.agent_id)
      addLog('info', `指定代理商: ${agent?.nickname || agent?.username}`)
    }
    
    if (simulatorForm.user_id) {
      const user = userList.value.find(u => u.id === simulatorForm.user_id)
      addLog('info', `指定用户: ${user?.nickname || '未设置昵称'} (ID: ${user?.id})`)
    }
    
    const requestData: CouponSimulateRequest = {
      quantity: simulatorForm.quantity,
      ...(simulatorForm.agent_id && { agent_id: simulatorForm.agent_id }),
      ...(simulatorForm.user_id && { user_id: simulatorForm.user_id })
    }
    
    const response = await simulateCouponUsage(requestData)
    
    if (response.code === 0) {
      simulationResult.value = response.data
      simulationStats.total += simulatorForm.quantity
      simulationStats.success += response.data.success_count
      
      addLog('success', `模拟完成！成功: ${response.data.success_count}, 失败: ${response.data.failed_count}`)
      
      // 显示详细结果
      if (response.data.details) {
        response.data.details.forEach((detail: any) => {
          if (detail.status === 'success') {
            addLog('success', `✓ 优惠券 ${detail.coupon_code} 使用成功`)
          } else {
            addLog('error', `✗ 优惠券 ${detail.coupon_code} 使用失败: ${detail.message}`)
          }
        })
      }
      
      ElMessage.success('模拟完成')
    } else {
      addLog('error', `模拟失败: ${response.message}`)
      ElMessage.error(response.message || '模拟失败')
    }
  } catch (error: any) {
    addLog('error', `模拟出错: ${error.message || '未知错误'}`)
    ElMessage.error(error.message || '模拟失败')
  } finally {
    simulatorLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAgents()
  addLog('info', '优惠券使用模拟器已就绪')
})
</script>

<style scoped>
/* 日志容器滚动条样式 */
.bg-gray-900::-webkit-scrollbar {
  width: 6px;
}

.bg-gray-900::-webkit-scrollbar-track {
  background: #374151;
}

.bg-gray-900::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 3px;
}

.bg-gray-900::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
