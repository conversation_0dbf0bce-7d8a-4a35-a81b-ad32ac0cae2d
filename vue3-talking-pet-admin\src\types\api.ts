/**
 * API 响应通用类型定义
 */

// 通用 API 响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页参数
export interface PaginationParams {
  page: number
  size: number
  keyword?: string
}

// 分页响应
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 排序参数
export interface SortParams {
  field: string
  order: 'asc' | 'desc'
}

// 通用查询参数
export interface QueryParams extends PaginationParams {
  sort?: SortParams
  filters?: Record<string, any>
}

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置
export interface RequestConfig {
  url: string
  method: HttpMethod
  data?: any
  params?: any
  headers?: Record<string, string>
}
