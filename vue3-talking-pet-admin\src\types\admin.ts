/**
 * 管理后台相关类型定义
 */

import type { UserRole } from './auth'

// 代理商信息
export interface Agent {
  id: number
  username: string
  nickname: string
  role_id: number
  role: UserRole
  parent_id?: number
  status: boolean
  created_at: string
  updated_at: string
  user_count?: number
  total_sales?: number
}

// 创建代理商请求
export interface CreateAgentRequest {
  username: string
  nickname: string
  password: string
}

// 更新代理商请求
export interface UpdateAgentRequest {
  nickname?: string
  status?: boolean
  password?: string
}

// 优惠券档次
export interface CouponTier {
  id: number
  name: string
  value: number
}

// 优惠券批次
export interface CouponBatch {
  id: number
  name: string
  tier_id: number
  tier?: CouponTier
  total_quantity: number
  issued_quantity: number
  valid_from: string
  valid_to: string
  created_by: number
  created_at: string
}

// 优惠券实例
export interface Coupon {
  id: number
  code: string
  batch_id: number
  batch?: CouponBatch
  owner_id?: number
  owner?: Agent
  status: 'AVAILABLE' | 'USED' | 'EXPIRED'
  used_at?: string
  used_by_user_id?: number
  used_in_order_id?: string
  assigned_at?: string
}

// 创建优惠券批次请求
export interface CreateCouponBatchRequest {
  name: string
  tier_id: number
  total_quantity: number
  valid_from: string
  valid_to: string
}

// 分配优惠券请求
export interface AssignCouponRequest {
  batch_id: number
  agent_id: number
  quantity: number
}

// C端用户信息
export interface EndUser {
  id: number
  nickname: string
  phone?: string
  avatar?: string
  registered_at: string
  agent_id?: number
  agent?: Agent
  total_orders?: number
  total_amount?: number
}

// 仪表盘统计数据
export interface DashboardStats {
  total_users: number
  total_agents: number
  total_sales: number
  today_new_users: number
  today_sales: number
  sales_trend: Array<{
    date: string
    amount: number
  }>
  agent_performance: Array<{
    agent_name: string
    sales: number
    percentage: number
  }>
}

// 优惠券使用模拟请求
export interface CouponSimulateRequest {
  agent_id?: number
  user_id?: number
  quantity: number
}
