<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <!-- 错误图标 -->
      <div class="mx-auto h-24 w-24 bg-red-100 rounded-full flex items-center justify-center mb-8">
        <el-icon :size="48" color="#F56C6C">
          <Lock />
        </el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="mb-8">
        <h1 class="text-6xl font-bold text-gray-900 mb-4">403</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">访问被拒绝</h2>
        <p class="text-gray-600">
          抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-4">
        <el-button type="primary" size="large" @click="goBack">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        
        <div>
          <el-button type="default" @click="goHome">
            <el-icon class="mr-2"><HomeFilled /></el-icon>
            返回首页
          </el-button>
        </div>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="text-sm font-medium text-blue-800 mb-2">需要帮助？</h3>
        <p class="text-sm text-blue-600">
          如果您认为这是一个错误，请联系系统管理员或检查您的账户权限设置。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Lock, ArrowLeft, HomeFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 返回上一页
function goBack() {
  router.go(-1)
}

// 返回首页
function goHome() {
  router.push('/dashboard')
}
</script>
