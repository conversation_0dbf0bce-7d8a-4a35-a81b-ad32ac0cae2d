/**
 * 认证相关 API
 */

import { post, get } from './index'
import type { 
  LoginRequest, 
  LoginResponse, 
  UserInfoResponse, 
  ChangePasswordRequest,
  AdminUser 
} from '@/types/auth'
import type { ApiResponse } from '@/types/api'

/**
 * 用户登录
 */
export function login(data: LoginRequest): Promise<LoginResponse> {
  return post<LoginResponse['data']>('/admin/auth/login', data)
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): Promise<UserInfoResponse> {
  return get<AdminUser>('/admin/auth/me')
}

/**
 * 修改密码
 */
export function changePassword(data: ChangePasswordRequest): Promise<ApiResponse> {
  return post('/admin/auth/change-password', data)
}

/**
 * 退出登录
 */
export function logout(): Promise<ApiResponse> {
  return post('/admin/auth/logout')
}

/**
 * 刷新 Token
 */
export function refreshToken(): Promise<LoginResponse> {
  return post<LoginResponse['data']>('/admin/auth/refresh')
}
