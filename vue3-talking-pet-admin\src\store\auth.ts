/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import type { AdminUser, LoginRequest, UserRole } from '@/types/auth'
import * as authAPI from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(getStoredToken())
  const user = ref<AdminUser | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userRole = computed((): UserRole | null => user.value?.role || null)
  const isSuper = computed(() => userRole.value === 'SuperAdmin')
  const isAgent = computed(() => userRole.value === 'Agent')
  const userName = computed(() => user.value?.nickname || user.value?.username || '')

  // 获取存储的 token
  function getStoredToken(): string | null {
    return localStorage.getItem('admin_token') || Cookies.get('admin_token') || null
  }

  // 存储 token
  function storeToken(tokenValue: string) {
    token.value = tokenValue
    localStorage.setItem('admin_token', tokenValue)
    Cookies.set('admin_token', tokenValue, {
      expires: 7, // 7天过期
      secure: false, // 开发环境设为 false
      sameSite: 'lax'
    })
  }

  // 清除 token
  function clearToken() {
    token.value = null
    user.value = null
    localStorage.removeItem('admin_token')
    Cookies.remove('admin_token')
  }

  // 登录
  async function login(credentials: LoginRequest) {
    loading.value = true
    try {
      const response = await authAPI.login(credentials)
      
      if (response.code === 0) {
        storeToken(response.data.token)
        user.value = response.data.user
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      clearToken()
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  async function logout() {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      clearToken()
    }
  }

  // 检查认证状态
  async function checkAuth(): Promise<boolean> {
    if (!token.value) {
      return false
    }

    try {
      const response = await authAPI.getCurrentUser()
      if (response.code === 0) {
        user.value = response.data
        return true
      } else {
        clearToken()
        return false
      }
    } catch (error) {
      console.error('Check auth error:', error)
      clearToken()
      return false
    }
  }

  // 修改密码
  async function changePassword(oldPassword: string, newPassword: string) {
    try {
      const response = await authAPI.changePassword({
        old_password: oldPassword,
        new_password: newPassword,
        confirm_password: newPassword
      })
      return response
    } catch (error) {
      throw error
    }
  }

  // 初始化认证状态
  async function initAuth() {
    if (token.value) {
      await checkAuth()
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isLoggedIn,
    userRole,
    isSuper,
    isAgent,
    userName,
    
    // 方法
    login,
    logout,
    checkAuth,
    changePassword,
    initAuth,
    clearToken
  }
})
