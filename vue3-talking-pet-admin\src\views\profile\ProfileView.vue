<template>
  <div class="profile">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">个人中心</h1>
      <p class="text-gray-600 mt-1">管理您的个人信息和账户设置</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 个人信息卡片 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="text-center">
            <el-avatar :size="80" :icon="UserFilled" class="mb-4" />
            <h3 class="text-lg font-semibold text-gray-900">{{ authStore.userName }}</h3>
            <p class="text-sm text-gray-500 mb-2">{{ authStore.user?.username }}</p>
            <el-tag :type="authStore.isSuper ? 'danger' : 'primary'">
              {{ authStore.isSuper ? '超级管理员' : '代理商' }}
            </el-tag>
          </div>
          
          <div class="mt-6 space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">用户ID</span>
              <span class="font-medium">{{ authStore.user?.id }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">创建时间</span>
              <span class="font-medium">{{ formatDate(authStore.user?.created_at) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">最后更新</span>
              <span class="font-medium">{{ formatDate(authStore.user?.updated_at) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">账户状态</span>
              <el-tag :type="authStore.user?.status ? 'success' : 'danger'" size="small">
                {{ authStore.user?.status ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置表单 -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <el-tabs v-model="activeTab" class="profile-tabs">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
                class="mt-4"
              >
                <el-form-item label="用户名">
                  <el-input
                    v-model="basicForm.username"
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
                
                <el-form-item label="昵称" prop="nickname">
                  <el-input
                    v-model="basicForm.nickname"
                    placeholder="请输入昵称"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="basicLoading"
                    @click="handleUpdateBasic"
                  >
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 修改密码 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="mt-4"
              >
                <el-form-item label="原密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入原密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="passwordLoading"
                    @click="handleChangePassword"
                  >
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 我的统计 (仅代理商) -->
            <el-tab-pane v-if="authStore.isAgent" label="我的统计" name="stats">
              <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center">
                      <div class="p-2 bg-blue-100 rounded-lg">
                        <el-icon :size="20" color="#409EFF"><User /></el-icon>
                      </div>
                      <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">我的用户数</p>
                        <p class="text-2xl font-bold text-blue-600">{{ agentStats.userCount }}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="bg-green-50 rounded-lg p-4">
                    <div class="flex items-center">
                      <div class="p-2 bg-green-100 rounded-lg">
                        <el-icon :size="20" color="#67C23A"><Money /></el-icon>
                      </div>
                      <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">我的销售额</p>
                        <p class="text-2xl font-bold text-green-600">¥{{ agentStats.totalSales.toLocaleString() }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="text-md font-medium text-gray-900 mb-3">优惠券统计</h4>
                  <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                      <div class="text-lg font-bold text-blue-600">{{ agentStats.coupons.available }}</div>
                      <div class="text-sm text-gray-600">可用</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-orange-600">{{ agentStats.coupons.used }}</div>
                      <div class="text-sm text-gray-600">已使用</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-red-600">{{ agentStats.coupons.expired }}</div>
                      <div class="text-sm text-gray-600">已过期</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UserFilled, User, Money } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('basic')
const basicLoading = ref(false)
const passwordLoading = ref(false)
const basicFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 基本信息表单
interface BasicForm {
  username: string
  nickname: string
}

const basicForm = reactive<BasicForm>({
  username: '',
  nickname: ''
})

// 密码表单
interface PasswordForm {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

const passwordForm = reactive<PasswordForm>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 代理商统计数据
const agentStats = reactive({
  userCount: 0,
  totalSales: 0,
  coupons: {
    available: 0,
    used: 0,
    expired: 0
  }
})

// 基本信息验证规则
const basicRules: FormRules<BasicForm> = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules: FormRules<PasswordForm> = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
function initFormData() {
  if (authStore.user) {
    basicForm.username = authStore.user.username
    basicForm.nickname = authStore.user.nickname || ''
  }
}

// 更新基本信息
async function handleUpdateBasic() {
  if (!basicFormRef.value) return
  
  try {
    await basicFormRef.value.validate()
    basicLoading.value = true
    
    // 这里应该调用更新用户信息的API
    // const response = await updateUserInfo({ nickname: basicForm.nickname })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('基本信息更新成功')
    
    // 更新store中的用户信息
    if (authStore.user) {
      authStore.user.nickname = basicForm.nickname
    }
  } catch (error: any) {
    ElMessage.error(error.message || '更新失败')
  } finally {
    basicLoading.value = false
  }
}

// 修改密码
async function handleChangePassword() {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    await authStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword
    )
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error: any) {
    ElMessage.error(error.message || '密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 重置密码表单
function resetPasswordForm() {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.resetFields()
}

// 获取代理商统计数据
async function fetchAgentStats() {
  if (!authStore.isAgent) return
  
  try {
    // 这里应该调用获取代理商统计数据的API
    // const response = await getAgentStats()
    
    // 模拟数据
    agentStats.userCount = 156
    agentStats.totalSales = 89650
    agentStats.coupons.available = 45
    agentStats.coupons.used = 123
    agentStats.coupons.expired = 12
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 格式化日期
function formatDate(date?: string) {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
  fetchAgentStats()
})
</script>

<style scoped>
.profile-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.profile-tabs :deep(.el-tab-pane) {
  padding: 0;
}
</style>
