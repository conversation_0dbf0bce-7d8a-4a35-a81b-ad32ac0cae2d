/**
 * 路由守卫
 */

import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/store/auth'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()

    // 如果是登录页面，且已登录，则重定向到仪表盘
    if (to.path === '/login' && authStore.isLoggedIn) {
      next('/dashboard')
      return
    }

    // 如果访问需要认证的页面
    if (to.meta.requiresAuth) {
      if (!authStore.token) {
        ElMessage.warning('请先登录')
        next('/login')
        return
      }

      // 验证 token 有效性
      const isValid = await authStore.checkAuth()
      if (!isValid) {
        ElMessage.error('登录已过期，请重新登录')
        next('/login')
        return
      }

      // 检查角色权限
      if (to.meta.roles && Array.isArray(to.meta.roles)) {
        if (!to.meta.roles.includes(authStore.userRole)) {
          ElMessage.error('您没有权限访问此页面')
          next('/403')
          return
        }
      }
    }

    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} | AI玩偶后台管理系统`
    }

    next()
  })

  // 全局后置守卫
  router.afterEach((to) => {
    // 页面加载完成后的处理
    console.log(`Navigated to: ${to.path}`)
  })
}
