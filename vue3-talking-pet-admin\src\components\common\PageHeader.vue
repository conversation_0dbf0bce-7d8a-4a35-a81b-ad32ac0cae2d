<template>
  <div class="page-header">
    <div class="flex items-center justify-between mb-6">
      <!-- 左侧：标题和描述 -->
      <div class="flex-1">
        <div class="flex items-center space-x-3">
          <!-- 返回按钮 -->
          <el-button
            v-if="showBack"
            :icon="ArrowLeft"
            circle
            size="small"
            @click="handleBack"
          />
          
          <!-- 图标 -->
          <div v-if="icon" class="flex-shrink-0">
            <div class="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <el-icon :size="18" color="#409EFF">
                <component :is="icon" />
              </el-icon>
            </div>
          </div>
          
          <!-- 标题和描述 -->
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
            <p v-if="description" class="text-gray-600 mt-1">{{ description }}</p>
          </div>
        </div>
        
        <!-- 面包屑 -->
        <div v-if="breadcrumbs && breadcrumbs.length > 0" class="mt-3">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="(item, index) in breadcrumbs"
              :key="index"
              :to="item.to"
            >
              {{ item.label }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
      
      <!-- 右侧：操作按钮 -->
      <div v-if="$slots.actions" class="flex-shrink-0 ml-6">
        <slot name="actions" />
      </div>
    </div>
    
    <!-- 额外内容 -->
    <div v-if="$slots.extra" class="mb-6">
      <slot name="extra" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

interface BreadcrumbItem {
  label: string
  to?: string | { name: string; params?: any; query?: any }
}

interface Props {
  title: string
  description?: string
  icon?: any
  showBack?: boolean
  breadcrumbs?: BreadcrumbItem[]
}

const props = withDefaults(defineProps<Props>(), {
  showBack: false
})

const router = useRouter()

// 返回上一页
function handleBack() {
  router.go(-1)
}
</script>

<style scoped>
.page-header {
  /* 可以添加一些基础样式 */
}
</style>
