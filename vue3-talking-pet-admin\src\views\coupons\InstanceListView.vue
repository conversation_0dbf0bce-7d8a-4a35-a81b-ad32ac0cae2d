<template>
  <div class="coupon-instance-list">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">优惠券实例管理</h1>
      <p class="text-gray-600 mt-1">查看和管理所有优惠券实例</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <el-icon :size="20" color="#409EFF"><Tickets /></el-icon>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600">总数量</p>
            <p class="text-lg font-bold text-gray-900">{{ stats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <el-icon :size="20" color="#67C23A"><CircleCheck /></el-icon>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600">可用</p>
            <p class="text-lg font-bold text-green-600">{{ stats.available }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 rounded-lg">
            <el-icon :size="20" color="#E6A23C"><CircleClose /></el-icon>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600">已使用</p>
            <p class="text-lg font-bold text-orange-600">{{ stats.used }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <el-icon :size="20" color="#F56C6C"><Clock /></el-icon>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600">已过期</p>
            <p class="text-lg font-bold text-red-600">{{ stats.expired }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索优惠券码"
          :prefix-icon="Search"
          clearable
          @keyup.enter="handleSearch"
        />
        
        <el-select v-model="searchForm.batchId" placeholder="选择批次" clearable>
          <el-option
            v-for="batch in batchList"
            :key="batch.id"
            :label="batch.name"
            :value="batch.id"
          />
        </el-select>
        
        <el-select v-model="searchForm.ownerId" placeholder="选择代理商" clearable>
          <el-option
            v-for="agent in agentList"
            :key="agent.id"
            :label="agent.nickname || agent.username"
            :value="agent.id"
          />
        </el-select>
        
        <el-select v-model="searchForm.status" placeholder="状态" clearable>
          <el-option label="可用" value="AVAILABLE" />
          <el-option label="已使用" value="USED" />
          <el-option label="已过期" value="EXPIRED" />
        </el-select>
        
        <div class="flex gap-2">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 优惠券列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <el-table
        v-loading="loading"
        :data="couponList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="code" label="优惠券码" min-width="150">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                {{ row.code }}
              </code>
              <el-button
                size="small"
                type="primary"
                link
                :icon="CopyDocument"
                @click="handleCopy(row.code)"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="batch" label="所属批次" min-width="150">
          <template #default="{ row }">
            <div v-if="row.batch">
              <div class="font-medium">{{ row.batch.name }}</div>
              <div class="text-sm text-gray-500">{{ row.batch.tier?.name }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="owner" label="拥有者" min-width="120">
          <template #default="{ row }">
            <div v-if="row.owner">
              <div class="font-medium">{{ row.owner.nickname || row.owner.username }}</div>
              <div class="text-sm text-gray-500">代理商</div>
            </div>
            <span v-else class="text-gray-400">未分配</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="使用信息" min-width="200">
          <template #default="{ row }">
            <div v-if="row.status === 'USED'" class="text-sm">
              <div>用户ID: {{ row.used_by_user_id }}</div>
              <div>订单ID: {{ row.used_in_order_id }}</div>
              <div>使用时间: {{ formatDateTime(row.used_at) }}</div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="assigned_at" label="分配时间" width="180">
          <template #default="{ row }">
            <span v-if="row.assigned_at">
              {{ formatDateTime(row.assigned_at) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Refresh, 
  CopyDocument,
  Tickets,
  CircleCheck,
  CircleClose,
  Clock
} from '@element-plus/icons-vue'
import { 
  getCoupons, 
  getCouponBatches,
  getAgents,
  getCouponStats
} from '@/api/admin'
import type { 
  Coupon, 
  CouponBatch,
  Agent
} from '@/types/admin'
import type { QueryParams } from '@/types/api'
import dayjs from 'dayjs'

const route = useRoute()

// 响应式数据
const loading = ref(false)
const couponList = ref<Coupon[]>([])
const batchList = ref<CouponBatch[]>([])
const agentList = ref<Agent[]>([])

// 统计数据
const stats = reactive({
  total: 0,
  available: 0,
  used: 0,
  expired: 0
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  batchId: undefined as number | undefined,
  ownerId: undefined as number | undefined,
  status: undefined as string | undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取批次列表
async function fetchBatches() {
  try {
    const response = await getCouponBatches({ page: 1, size: 1000 })
    if (response.code === 0) {
      batchList.value = response.data.items
    }
  } catch (error) {
    console.error('获取批次列表失败:', error)
  }
}

// 获取代理商列表
async function fetchAgents() {
  try {
    const response = await getAgents({ page: 1, size: 1000 })
    if (response.code === 0) {
      agentList.value = response.data.items
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
  }
}

// 获取统计数据
async function fetchStats() {
  try {
    const response = await getCouponStats()
    if (response.code === 0) {
      Object.assign(stats, response.data)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取优惠券列表
async function fetchCoupons() {
  try {
    loading.value = true
    const params: QueryParams = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      filters: {
        ...(searchForm.batchId && { batch_id: searchForm.batchId }),
        ...(searchForm.ownerId && { owner_id: searchForm.ownerId }),
        ...(searchForm.status && { status: searchForm.status })
      }
    }
    
    const response = await getCoupons(params)
    if (response.code === 0) {
      couponList.value = response.data.items
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    ElMessage.error('获取优惠券列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  fetchCoupons()
}

// 重置搜索
function handleReset() {
  searchForm.keyword = ''
  searchForm.batchId = undefined
  searchForm.ownerId = undefined
  searchForm.status = undefined
  pagination.page = 1
  fetchCoupons()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchCoupons()
}

// 当前页变化
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchCoupons()
}

// 复制优惠券码
async function handleCopy(code: string) {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('优惠券码已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = code
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('优惠券码已复制到剪贴板')
  }
}

// 获取状态类型
function getStatusType(status: string) {
  switch (status) {
    case 'AVAILABLE':
      return 'success'
    case 'USED':
      return 'warning'
    case 'EXPIRED':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
function getStatusText(status: string) {
  switch (status) {
    case 'AVAILABLE':
      return '可用'
    case 'USED':
      return '已使用'
    case 'EXPIRED':
      return '已过期'
    default:
      return '未知'
  }
}

// 格式化日期时间
function formatDateTime(date: string) {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载时获取数据
onMounted(() => {
  // 如果有批次ID参数，设置默认筛选
  const batchId = route.query.batch_id
  if (batchId) {
    searchForm.batchId = Number(batchId)
  }
  
  fetchBatches()
  fetchAgents()
  fetchStats()
  fetchCoupons()
})
</script>

<style scoped>
code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
